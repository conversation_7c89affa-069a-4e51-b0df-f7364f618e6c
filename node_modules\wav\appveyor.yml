# http://www.appveyor.com/docs/appveyor-yml

# Test against these versions of Node.js.
environment:
  matrix:
    - nodejs_version: '6'
    - nodejs_version: '4'
    - nodejs_version: '0.12'
    - nodejs_version: '0.10'

# Install scripts. (runs after repo cloning)
install:
  # Get the latest stable version of Node 0.STABLE.latest
  - ps: Update-NodeJsInstallation (Get-NodeJsLatestBuild $env:nodejs_version)
  # Typical npm stuff.
  - npm install

# Post-install test scripts.
test_script:
  # Output useful info for debugging.
  - node --version
  - npm --version
  # We test multiple Windows shells because of Node.js stdout buffering issues:
  # https://github.com/joyent/node/issues/3584
  - ps: "npm test # PowerShell" # Pass comment to PS for easier debugging
  - cmd: npm test

# Don't actually build.
build: off

# Set build version format here instead of in the admin panel.
version: "{build}"
