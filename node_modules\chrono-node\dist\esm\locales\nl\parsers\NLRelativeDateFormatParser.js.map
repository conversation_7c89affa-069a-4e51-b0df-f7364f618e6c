{"version": 3, "file": "NLRelativeDateFormatParser.js", "sourceRoot": "", "sources": ["../../../../../src/locales/nl/parsers/NLRelativeDateFormatParser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,oBAAoB,EAAE,MAAM,cAAc,CAAC;AAEpD,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAC;AACrD,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,sCAAsC,EAAE,MAAM,wDAAwD,CAAC;AAChH,OAAO,EAAE,eAAe,EAAE,MAAM,wBAAwB,CAAC;AAEzD,MAAM,OAAO,GAAG,IAAI,MAAM,CACtB,2DAA2D,eAAe,CAAC,oBAAoB,CAAC,WAAW;IACvG,WAAW,EACf,GAAG,CACN,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAC9B,MAAM,mBAAmB,GAAG,CAAC,CAAC;AAE9B,MAAM,CAAC,OAAO,OAAO,0BAA2B,SAAQ,sCAAsC;IAC1F,YAAY;QACR,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,YAAY,CAAC,OAAuB,EAAE,KAAuB;QACzD,MAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1D,MAAM,QAAQ,GAAG,KAAK,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,CAAC;QAC1D,MAAM,QAAQ,GAAG,oBAAoB,CAAC,QAAQ,CAAC,CAAC;QAEhD,IAAI,QAAQ,IAAI,SAAS,IAAI,QAAQ,IAAI,QAAQ,IAAI,QAAQ,IAAI,WAAW,EAAE,CAAC;YAC3E,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACxB,OAAO,iBAAiB,CAAC,2BAA2B,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACvF,CAAC;QAED,IAAI,QAAQ,IAAI,WAAW,IAAI,QAAQ,IAAI,OAAO,EAAE,CAAC;YACjD,MAAM,SAAS,GAAG,EAAE,CAAC;YACrB,SAAS,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YACzB,OAAO,iBAAiB,CAAC,2BAA2B,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;QACvF,CAAC;QAED,MAAM,UAAU,GAAG,OAAO,CAAC,uBAAuB,EAAE,CAAC;QACrD,IAAI,IAAI,GAAG,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;QAG5C,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YAC1B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;YACrC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,UAAU,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC1C,CAAC;aAGI,IAAI,QAAQ,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YAChC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;YACvC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACvC,UAAU,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;QACjD,CAAC;aAGI,IAAI,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC;YAC/B,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;YACvC,IAAI,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,EAAE,EAAE,OAAO,CAAC,CAAC;YAExC,UAAU,CAAC,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YACrC,UAAU,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;YAC5C,UAAU,CAAC,MAAM,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;QAC3C,CAAC;QAED,OAAO,UAAU,CAAC;IACtB,CAAC;CACJ"}