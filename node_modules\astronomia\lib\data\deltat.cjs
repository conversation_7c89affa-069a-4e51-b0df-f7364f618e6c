'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

/**
 * DO NOT EDIT MANUALLY
 * Use `scripts/deltat.js` to generate file.
 * Datasets are from <https://maia.usno.navy.mil/ser7> and
 * <ftp://ftp.iers.org/products/eop/rapid/standard>
 */
const m = {
  historic: {
    table: [44, 43, 43, 41, 40, 39, 38, 37, 37, 36, 36, 36, 37, 37, 38, 37, 36, 36, 35, 35, 34, 33, 33, 32, 32, 31, 31, 30, 30, 29, 29, 29, 29, 29, 28, 28, 27, 27, 26, 26, 25, 25, 25, 26, 26, 26, 26, 25, 25, 25, 24, 24, 24, 24, 24, 24, 24, 24, 24, 24, 23, 23, 23, 23, 22, 22, 22, 22, 22, 21, 21, 21, 21, 21, 21, 21, 21, 20, 20, 20, 20, 20, 20, 20, 20, 20, 21, 21, 21, 20, 20, 20, 20, 20, 19, 19, 19, 19, 19, 20, 20, 20, 20, 19, 20, 20, 20, 20, 20, 20, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21, 21.1, 21, 21, 21, 20.9, 20.8, 20.7, 20.6, 20.4, 20.2, 20, 19.7, 19.4, 19.1, 18.7, 18.3, 17.8, 17.4, 17, 16.8, 16.6, 16.4, 16.1, 15.9, 15.7, 15.5, 15.3, 15, 14.7, 14.5, 14.3, 14.2, 14.1, 14.1, 14.1, 13.9, 13.7, 13.6, 13.5, 13.5, 13.5, 13.5, 13.4, 13.4, 13.4, 13.4, 13.3, 13.3, 13.2, 13.2, 13.2, 13.1, 13.1, 13.1, 13, 13.2, 13.3, 13.4, 13.5, 13.6, 13.7, 13.8, 13.9, 14, 14, 14.1, 14.1, 14.1, 14.1, 14.2, 14.3, 14.4, 14.4, 14.5, 14.6, 14.6, 14.7, 14.7, 14.7, 14.8, 14.8, 14.9, 14.9, 15, 15, 15.1, 15.2, 15.3, 15.4, 15.5, 15.6, 15.6, 15.6, 15.8, 15.9, 15.9, 15.9, 15.8, 15.7, 15.8, 15.7, 15.7, 15.7, 15.8, 15.9, 16.1, 16.1, 16, 15.9, 15.9, 15.7, 15.4, 15.3, 15.4, 15.5, 15.6, 15.6, 15.6, 15.6, 15.6, 15.6, 15.6, 15.5, 15.5, 15.4, 15.3, 15.2, 15.1, 14.9, 14.8, 14.6, 14.4, 14.3, 14.2, 14.1, 14.2, 14.2, 13.9, 13.7, 13.5, 13.3, 13.1, 13, 13.2, 13.2, 13.1, 13.1, 13.2, 13.3, 13.5, 13.5, 13.4, 13.2, 13.2, 13.1, 13.1, 13, 12.8, 12.6, 12.7, 12.6, 12.3, 12, 11.9, 11.8, 11.6, 11.4, 11.2, 11.1, 11.1, 11.1, 11.1, 11.1, 11.2, 11.1, 11.1, 11.2, 11.4, 11.5, 11.3, 11.2, 11.4, 11.7, 11.9, 11.9, 11.9, 11.8, 11.7, 11.8, 11.8, 11.8, 11.7, 11.6, 11.6, 11.5, 11.5, 11.4, 11.4, 11.3, 11.3, 11.13, 11.16, 10.94, 10.72, 10.29, 10.04, 9.94, 9.91, 9.88, 9.86, 9.72, 9.67, 9.66, 9.64, 9.51, 9.4, 9.21, 9, 8.6, 8.29, 7.95, 7.73, 7.59, 7.49, 7.36, 7.26, 7.1, 7, 6.89, 6.82, 6.73, 6.64, 6.39, 6.28, 6.25, 6.27, 6.25, 6.27, 6.22, 6.24, 6.22, 6.27, 6.3, 6.36, 6.35, 6.37, 6.32, 6.33, 6.33, 6.37, 6.37, 6.41, 6.4, 6.44, 6.46, 6.51, 6.48, 6.51, 6.53, 6.58, 6.55, 6.61, 6.69, 6.8, 6.84, 6.94, 7.03, 7.13, 7.15, 7.22, 7.26, 7.3, 7.23, 7.22, 7.21, 7.2, 6.99, 6.98, 7.19, 7.36, 7.35, 7.39, 7.41, 7.45, 7.36, 7.18, 6.95, 6.72, 6.45, 6.24, 5.92, 5.59, 5.15, 4.67, 4.11, 3.52, 2.94, 2.47, 1.97, 1.52, 1.04, 0.6, 0.11, -0.34, -0.82, -1.25, -1.7, -2.08, -2.48, -2.82, -3.19, -3.5, -3.84, -4.14, -4.43, -4.59, -4.79, -4.92, -5.09, -5.24, -5.36, -5.34, -5.37, -5.32, -5.34, -5.33, -5.4, -5.47, -5.58, -5.66, -5.74, -5.68, -5.69, -5.65, -5.67, -5.68, -5.73, -5.72, -5.78, -5.79, -5.86, -5.89, -6.01, -6.13, -6.28, -6.41, -6.53, -6.49, -6.5, -6.45, -6.41, -6.26, -6.11, -5.9, -5.63, -5.13, -4.68, -4.19, -3.72, -3.21, -2.7, -2.09, -1.48, -0.75, -0.08, 0.62, 1.26, 1.95, 2.59, 3.28, 3.92, 4.61, 5.2, 5.73, 6.29, 7, 7.68, 8.45, 9.13, 9.78, 10.38, 10.99, 11.64, 12.47, 13.23, 14, 14.69, 15.38, 16, 16.64, 17.19, 17.72, 18.19, 18.67, 19.13, 19.69, 20.14, 20.54, 20.86, 21.14, 21.41, 21.78, 22.06, 22.3, 22.51, 22.79, 23.01, 23.29, 23.46, 23.55, 23.63, 23.8, 23.95, 24.25, 24.39, 24.42, 24.34, 24.22, 24.1, 24.08, 24.02, 24.04, 23.98, 23.91, 23.89, 23.95, 23.93, 23.92, 23.88, 23.94, 23.91, 23.82, 23.76, 23.87, 23.91, 23.95, 23.96, 24, 24.04, 24.2, 24.35, 24.61, 24.82, 25.09, 25.3, 25.56, 25.77, 26.05, 26.27, 26.54, 26.76, 27.04, 27.27, 27.55, 27.77, 28.03, 28.25, 28.5, 28.7, 28.95, 29.15, 29.38, 29.57, 29.8, 29.97, 30.19, 30.36, 30.57, 30.72, 30.93, 31.07, 31.24, 31.349, 31.516, 31.677, 31.923, 32.166, 32.449, 32.671, 32.919, 33.15, 33.397, 33.584, 33.804, 33.992, 34.24, 34.466, 34.731, 35.03, 35.4, 35.738, 36.147, 36.546, 36.995, 37.429, 37.879, 38.291, 38.753, 39.204, 39.707, 40.182, 40.706, 41.17, 41.686, 42.227, 42.825, 43.373, 43.959, 44.486, 44.997, 45.477, 45.983, 46.458, 46.997, 47.521, 48.034, 48.535, 49.099, 49.589, 50.102, 50.54, 50.975, 51.382, 51.81, 52.168, 52.572, 52.957, 53.434, 53.789, 54.087],
    first: 1657,
    last: 1984.5
  },
  data: {
    table: [43.4724372, 43.5648351, 43.6736863, 43.7782156, 43.8763273, 43.9562443, 44.0314956, 44.1131788, 44.1982187, 44.2951747, 44.3936471, 44.4840562, 44.5646335, 44.6425099, 44.7385767, 44.8370135, 44.9302138, 44.9986146, 45.0583549, 45.1283911, 45.2063835, 45.2980068, 45.3897017, 45.476138, 45.5632485, 45.6450189, 45.7374593, 45.8283721, 45.9132976, 45.9819705, 46.0407484, 46.1067084, 46.1825041, 46.2788561, 46.3713351, 46.4567207, 46.544486, 46.6310899, 46.7302231, 46.8283588, 46.9247443, 46.9969757, 47.0709148, 47.1450515, 47.2361542, 47.3413241, 47.4319364, 47.5213815, 47.6049313, 47.6837388, 47.7781381, 47.8770756, 47.9687104, 48.0348257, 48.0942021, 48.1608205, 48.2460028, 48.3438529, 48.4355405, 48.5344163, 48.6324506, 48.7293718, 48.8365414, 48.9353232, 49.0318781, 49.1013205, 49.1590844, 49.2285534, 49.3069683, 49.4017939, 49.4945263, 49.5861495, 49.6804907, 49.7602264, 49.8555805, 49.9489224, 50.0346777, 50.1018531, 50.1621723, 50.2260014, 50.2967905, 50.3831254, 50.4598772, 50.5387068, 50.6160484, 50.6865941, 50.7658362, 50.8453698, 50.918672, 50.9761148, 51.0278017, 51.084323, 51.1537928, 51.2318645, 51.306308, 51.3807849, 51.4526292, 51.5160394, 51.5985479, 51.680924, 51.7572854, 51.8133335, 51.8532385, 51.9014358, 51.9603433, 52.0328072, 52.0984957, 52.1667826, 52.2316418, 52.2938376, 52.3679897, 52.4465221, 52.5179552, 52.5751485, 52.6178012, 52.666816, 52.7340036, 52.8055792, 52.8792189, 52.9564838, 53.0444971, 53.126769, 53.2196749, 53.3024139, 53.3746645, 53.4335399, 53.4778015, 53.5299937, 53.5845392, 53.6522628, 53.7255844, 53.7882418, 53.8366625, 53.8829665, 53.9442904, 54.0042478, 54.0536342, 54.085644, 54.1084122, 54.1462942, 54.1913988, 54.2452023, 54.2957622, 54.3427024, 54.3910864, 54.4319877, 54.4897699, 54.545636, 54.597741, 54.6354962, 54.6532352, 54.677594, 54.7173643, 54.7740957, 54.8253023, 54.8712512, 54.916146, 54.9580535, 54.9997186, 55.047571, 55.0911778, 55.1132386, 55.132774, 55.1532423, 55.1898003, 55.2415531, 55.283803, 55.3222105, 55.3612676, 55.406262, 55.4628719, 55.5110909, 55.5523777, 55.5811877, 55.6004372, 55.626202, 55.6656271, 55.7167999, 55.7698097, 55.8196609, 55.8615028, 55.9129883, 55.9663474, 56.0220102, 56.0700015, 56.0939035, 56.110463, 56.1313736, 56.1610839, 56.2068432, 56.2582503, 56.3000349, 56.339902, 56.3789995, 56.4282839, 56.4803947, 56.5352164, 56.5697487, 56.5983102, 56.6328326, 56.6738814, 56.7332116, 56.7971596, 56.8552701, 56.9111378, 56.9754725, 57.0470772, 57.1136128, 57.173831, 57.2226068, 57.259731, 57.3072742, 57.3643368, 57.4334281, 57.5015747, 57.5653127, 57.6333396, 57.6972844, 57.7710774, 57.8407427, 57.9057801, 57.9575663, 57.9974929, 58.0425517, 58.1043319, 58.1679128, 58.2389092, 58.3091659, 58.3833021, 58.4536748, 58.5401438, 58.6227714, 58.6916662, 58.7409628, 58.7836189, 58.8405543, 58.898579, 58.9713678, 59.043837, 59.1218414, 59.2002687, 59.274737, 59.3574134, 59.4433827, 59.5242416, 59.5849787, 59.6343497, 59.6927827, 59.758805, 59.8386448, 59.9110567, 59.9844537, 60.056435, 60.123065, 60.2042185, 60.2803745, 60.3530352, 60.4011891, 60.4439959, 60.4900257, 60.5578054, 60.6324446, 60.7058569, 60.7853482, 60.8663504, 60.9386672, 61.0276757, 61.1103448, 61.1870458, 61.2453891, 61.2881024, 61.3377799, 61.4036165, 61.4760366, 61.5524599, 61.6286593, 61.6845819, 61.743306, 61.8132425, 61.8823203, 61.9496762, 61.9968743, 62.0342938, 62.0714108, 62.1202315, 62.1809508, 62.2382046, 62.2950486, 62.3506479, 62.3995381, 62.475395, 62.5463091, 62.6136031, 62.6570739, 62.6941742, 62.7383271, 62.7926305, 62.8566986, 62.9145607, 62.9658689, 63.0216632, 63.0807052, 63.1461718, 63.2052946, 63.2599441, 63.2844088, 63.2961369, 63.3126092, 63.3421622, 63.3871303, 63.4339302, 63.4673369, 63.4978642, 63.5319327, 63.5679441, 63.6104432, 63.6444291, 63.6641815, 63.6739403, 63.692603, 63.7147066, 63.7518055, 63.792717, 63.8285221, 63.8556871, 63.8803854, 63.9075025, 63.9392787, 63.9690744, 63.9798604, 63.9833077, 63.9938011, 64.0093384, 64.0399621, 64.0670429, 64.0907881, 64.1068077, 64.1282125, 64.1584211, 64.1832722, 64.2093975, 64.2116628, 64.2073173, 64.2115565, 64.2222858, 64.2499625, 64.2760973, 64.2998037, 64.3191858, 64.345013, 64.3734584, 64.3943291, 64.4151156, 64.4132064, 64.4118464, 64.4096536, 64.4167832, 64.43292, 64.4510529, 64.4734276, 64.4893377, 64.5053342, 64.5269189, 64.5470942, 64.5596729, 64.5512293, 64.5370906, 64.5359472, 64.5414947, 64.5543634, 64.5654298, 64.5736111, 64.5891142, 64.6014759, 64.6176147, 64.6374397, 64.6548674, 64.6530021, 64.6379271, 64.637161, 64.6399614, 64.6543152, 64.6723164, 64.6876311, 64.7051905, 64.7313433, 64.7575312, 64.7811143, 64.8000929, 64.7994561, 64.7876424, 64.783095, 64.7920604, 64.8096421, 64.8310888, 64.8451826, 64.8597013, 64.8849929, 64.9174991, 64.9480298, 64.9793881, 64.9894772, 65.0028155, 65.0138193, 65.0371432, 65.0772597, 65.112197, 65.1464034, 65.1832638, 65.2145358, 65.2493713, 65.2920645, 65.3279403, 65.3413366, 65.3451881, 65.34964, 65.3711307, 65.3971998, 65.4295547, 65.4573487, 65.486752, 65.5152012, 65.5449916, 65.5780768, 65.612728, 65.6287505, 65.6370091, 65.6493375, 65.6759928, 65.7096966, 65.746092, 65.7768362, 65.8024614, 65.8236695, 65.8595036, 65.8973008, 65.932291, 65.950911, 65.9534105, 65.962833, 65.9838647, 66.0146733, 66.042049, 66.0699217, 66.0961343, 66.1310116, 66.1682713, 66.2071627, 66.2355846, 66.2408549, 66.2335423, 66.2349107, 66.2441095, 66.2751123, 66.3054334, 66.3245568, 66.3405713, 66.3624433, 66.3957101, 66.428903, 66.4618675, 66.4748837, 66.4751281, 66.4828678, 66.5056165, 66.5382912, 66.5705628, 66.6030198, 66.6339689, 66.6569117, 66.6925011, 66.7288729, 66.7578719, 66.7707625, 66.7740427, 66.7846288, 66.810324, 66.840048, 66.8778601, 66.9069091, 66.944259, 66.9762508, 67.0258126, 67.0716286, 67.1100184, 67.1266401, 67.1331391, 67.145797, 67.17174, 67.2091069, 67.2459812, 67.2810383, 67.3136452, 67.3456968, 67.389003, 67.4318433, 67.4666209, 67.4858459, 67.4989147, 67.5110936, 67.5352658, 67.571103, 67.6070253, 67.6439167, 67.6765272, 67.7116693, 67.7590634, 67.8011542, 67.840213, 67.8606318, 67.8821576, 67.9120101, 67.9546462, 68.0054839, 68.051412, 68.1024205, 68.1577127, 68.2043653, 68.2664507, 68.3188171, 68.3703564, 68.3964356, 68.4094472, 68.4304611, 68.4629791, 68.507818, 68.5537018, 68.5927179, 68.6298107, 68.6670627, 68.7135208, 68.7622755, 68.8032843, 68.8244838, 68.8373427, 68.847693, 68.8688567, 68.9005814, 68.9354999, 68.9676423, 68.9875354, 69.0175527, 69.0499081, 69.0823433, 69.1070034, 69.1134027, 69.1141898, 69.1207203, 69.1355578, 69.16459, 69.1964228, 69.2201632, 69.2451564, 69.2732758, 69.3031979, 69.3325675, 69.3540507, 69.3581722, 69.3441594, 69.3376329, 69.3377424, 69.3432191, 69.3540144, 69.3611554, 69.3751703, 69.3889904, 69.4091639, 69.4264662, 69.4386335, 69.4241335, 69.3921241, 69.3693422, 69.3574782, 69.3593242, 69.3630244, 69.359334, 69.3510133, 69.3537917, 69.3582217, 69.367306, 69.3678649, 69.3514228, 69.3273414, 69.3033273, 69.2892463, 69.2880419, 69.2908014, 69.2944974, 69.2913953, 69.286149, 69.2835153, 69.2815422, 69.2806375, 69.2553511, 69.2125426, 69.1847287, 69.17207, 69.1691531, 69.173303, 69.1698064, 69.1589095, 69.1556275, 69.1672253, 69.1771384],
    first: 1973.0849315068492,
    firstYM: [1973, 2],
    last: 2023.3287671232877,
    lastYM: [2023, 5]
  },
  prediction: {
    table: [67.87818, 67.96817999999999, 68.02817999999999, 68.04818, 68.12818, 68.21817999999999, 68.26818, 68.28818, 68.36818, 68.44818, 68.50818, 68.51818, 68.59818, 68.68818, 68.73818, 68.74817999999999, 68.82818, 68.91817999999999, 68.96817999999999, 68.98818, 69.06818, 69.14818, 69.20818, 69.22818, 69.30818, 69.39818, 69.46817999999999, 69.48818, 69.57818, 69.66817999999999, 69.73818, 69.75818, 69.85817999999999, 69.95818, 70.02817999999999, 70.05818, 70.15818, 70.25818, 70.33818, 70.36818, 70.46817999999999],
    first: 2022,
    last: 2032
  }
};

exports["default"] = m;
