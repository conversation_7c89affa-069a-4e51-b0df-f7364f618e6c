import axios from "axios";
import { extractReadableContent, getFaviconURL, getWebsiteName } from "./webutils.js";

export async function performSearchWithFallback(query) {
  try {
    const google = await searchGoogleCSE(query);
    return google;
  } catch (e) {
    console.error("Google CSE search failed:", e.message);
    return { text: "Sorry, search failed.", links: [], engine: "failed" };
  }
}

async function searchGoogleCSE(query) {
  const apiKey = process.env.GOOGLE_CSE_API_KEY;
  const cx = process.env.GOOGLE_CSE_ID;
  console.log(`Google CSE - API Key: ${apiKey ? 'Set' : 'Not set'}, CSE ID: ${cx ? 'Set' : 'Not set'}`);
  
  const url = `https://www.googleapis.com/customsearch/v1?q=${encodeURIComponent(query)}&key=${apiKey}&cx=${cx}`;
  console.log(`Google CSE URL: ${url}`);
  
  try {
    const res = await axios.get(url);
    const items = res.data.items || [];
    console.log(`Google CSE returned ${items.length} results`);
    
    return {
      text: items.map((r, i) => `${i + 1}. ${r.title}\n${r.snippet}\n(${r.link})`).join("\n\n"),
      links: items.map(r => ({
        title: r.title,
        snippet: r.snippet,
        link: r.link,
        favicon: getFaviconURL(r.link),
        website: getWebsiteName(r.link)
      })),
      engine: "google_cse"
    };
  } catch (error) {
    console.error('Google CSE error:', error.response?.data || error.message);
    throw error;
  }
}
