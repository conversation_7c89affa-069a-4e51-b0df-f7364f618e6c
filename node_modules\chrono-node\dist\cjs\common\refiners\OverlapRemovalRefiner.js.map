{"version": 3, "file": "OverlapRemovalRefiner.js", "sourceRoot": "", "sources": ["../../../../src/common/refiners/OverlapRemovalRefiner.ts"], "names": [], "mappings": ";;AAOA,MAAqB,qBAAqB;IACtC,MAAM,CAAC,OAAuB,EAAE,OAAwB;QACpD,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACrB,OAAO,OAAO,CAAC;QACnB,CAAC;QAED,MAAM,eAAe,GAAG,EAAE,CAAC;QAC3B,IAAI,UAAU,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;QAC5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACtC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,MAAM,CAAC,KAAK,IAAI,UAAU,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC5D,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBACjC,UAAU,GAAG,MAAM,CAAC;gBACpB,SAAS;YACb,CAAC;YAGD,IAAI,IAAI,GAAG,IAAI,CAAC;YAChB,IAAI,OAAO,GAAG,IAAI,CAAC;YACnB,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC;gBAC9C,IAAI,GAAG,MAAM,CAAC;gBACd,OAAO,GAAG,UAAU,CAAC;YACzB,CAAC;iBAAM,CAAC;gBACJ,IAAI,GAAG,UAAU,CAAC;gBAClB,OAAO,GAAG,MAAM,CAAC;YACrB,CAAC;YACD,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,OAAO,OAAO,IAAI,EAAE,CAAC,CAAC;YACzE,CAAC,CAAC,CAAC;YACH,UAAU,GAAG,IAAI,CAAC;QACtB,CAAC;QAGD,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;YACrB,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACrC,CAAC;QAED,OAAO,eAAe,CAAC;IAC3B,CAAC;CACJ;AAvCD,wCAuCC"}