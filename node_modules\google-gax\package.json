{"name": "google-gax", "version": "5.0.1", "description": "Google API Extensions", "main": "build/src/index.js", "types": "build/src/index.d.ts", "files": ["build/src", "build/protos/", "!build/src/**/*.map"], "dependencies": {"@grpc/grpc-js": "^1.12.6", "@grpc/proto-loader": "^0.7.13", "abort-controller": "^3.0.0", "duplexify": "^4.1.3", "google-auth-library": "^10.1.0", "google-logging-utils": "^1.1.1", "node-fetch": "^3.3.2", "object-hash": "^3.0.0", "proto3-json-serializer": "^3.0.0", "protobufjs": "^7.5.3", "retry-request": "^8.0.0"}, "devDependencies": {"@babel/plugin-proposal-private-methods": "^7.18.6", "@types/mocha": "^10.0.10", "@types/ncp": "^2.0.8", "@types/node": "^22.0.0", "@types/object-hash": "^3.0.6", "@types/proxyquire": "^1.3.31", "@types/pumpify": "^1.4.4", "@types/sinon": "^17.0.3", "@types/uglify-js": "^3.17.5", "c8": "^10.1.3", "codecov": "^3.8.3", "execa": "^5.0.0", "glob": "10.4.5", "google-proto-files": "^5.0.0", "gts": "^6.0.2", "jackspeak": "^3.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^4.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "long": "^5.2.4", "lru-cache": "^10.0.0", "mkdirp": "^3.0.1", "mocha": "^11.1.0", "ncp": "^2.0.0", "null-loader": "^4.0.1", "pdfmake": "^0.2.18", "proxyquire": "^2.1.3", "pumpify": "^2.0.1", "sinon": "^21.0.0", "stream-events": "^1.0.5", "ts-loader": "^8.0.0", "typescript": "^5.7.3", "uglify-js": "^3.19.3", "walkdir": "^0.4.1", "webpack": "^5.97.1", "webpack-cli": "^6.0.1"}, "scripts": {"docs": "jsdoc -c .jsdoc.js", "pretest": "npm run prepare", "test": "c8 mocha build/test/unit", "lint": "gts check src samples test", "clean": "gts clean", "compile": "tsc -p . && cp src/*.json build/src && cp -r test/fixtures build/test && cp -r protos build/", "compile-operation-protos": "pbjs -t json google/longrunning/operations.proto -p ./protos > protos/operations.json && pbjs -t static-module -r operations_protos google/longrunning/operations.proto -p ./protos > protos/operations.js && pbts protos/operations.js -o protos/operations.d.ts", "compile-compute-operations-protos": "pbjs -t json google/longrunning/compute_operations.proto -p ./protos > protos/compute_operations.json && pbjs -t static-module -r compute_operations_protos google/longrunning/compute_operations.proto -p ./protos > protos/compute_operations.js && pbts protos/compute_operations.js -o protos/compute_operations.d.ts", "compile-iam-protos": "pbjs -t json google/iam/v1/iam_policy.proto google/iam/v1/options.proto google/iam/v1/policy.proto google/iam/v1/logging/audit_data.proto -p ./protos > protos/iam_service.json && pbjs -t static-module -r iam_protos google/iam/v1/iam_policy.proto google/iam/v1/options.proto google/iam/v1/policy.proto google/iam/v1/logging/audit_data.proto -p ./protos > protos/iam_service.js && pbts protos/iam_service.js -o protos/iam_service.d.ts", "compile-location-protos": "pbjs -t json google/cloud/location/locations.proto -p ./protos > protos/locations.json && pbjs -t static-module -r locations_protos google/cloud/location/locations.proto -p ./protos > protos/locations.js && pbts protos/locations.js -o protos/locations.d.ts", "compile-status-protos": "pbjs -t json google/rpc/status.proto google/rpc/error_details.proto -p ./protos > protos/status.json", "compile-http-protos": "pbjs -t static-module -r http_proto --keep-case google/api/http.proto -p ./protos > protos/http.js && pbts protos/http.js -o protos/http.d.ts", "fix": "gts fix", "prepare": "npm run compile && cd ../tools && npm i && npm run compile && cd ../gax && pwd && node ../tools/build/src/prepublish.js . && mkdirp build/protos && cp -r protos/* build/protos/ && npm run minify-proto-json", "system-test": "c8 mocha build/test/system-test --timeout 600000 && npm run test-application", "samples-test": "cd samples/ && npm link ../ && npm test && cd ../", "docs-test": "linkinator docs", "predocs-test": "npm run docs", "browser-test": "cd test/browser-test && npm run prefetch && npm install && npm test", "test-application": "cd test/test-application && npm run prefetch && npm install && npm start", "prelint": "cd samples; npm link ../; npm install", "precompile": "gts clean", "update-protos": "cd ../tools && npm i && npm run compile && cd ../gax && node ../tools/build/src/listProtos.js .", "minify-proto-json": "cd ../tools && npm i && npm run compile && cd ../gax && node ../tools/build/src/minify.js"}, "keywords": ["grpc"], "exports": {".": "./build/src/index.js", "./fallback": "./build/src/fallback.js", "./gax": "./build/src/gax.js", "./build/src/protobuf": "./build/src/protobuf.js", "./build/src/index.js": "./build/src/index.js"}, "repository": {"type": "git", "url": "https://github.com/googleapis/gax-nodejs.git", "directory": "gax"}, "author": "Google API Authors", "license": "Apache-2.0", "bugs": {"url": "https://github.com/googleapis/gax-nodejs/issues"}, "homepage": "https://github.com/googleapis/gax-nodejs#readme", "engines": {"node": ">=18"}, "browser": "build/src/fallback.js"}