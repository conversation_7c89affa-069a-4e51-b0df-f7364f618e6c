{"version": 3, "file": "UnlikelyFormatFilter.js", "sourceRoot": "", "sources": ["../../../../src/common/refiners/UnlikelyFormatFilter.ts"], "names": [], "mappings": ";;AAAA,0DAA6C;AAG7C,MAAqB,oBAAqB,SAAQ,yBAAM;IACpD,YAAoB,UAAmB;QACnC,KAAK,EAAE,CAAC;QADQ,eAAU,GAAV,UAAU,CAAS;IAEvC,CAAC;IAED,OAAO,CAAC,OAAO,EAAE,MAAqB;QAClC,IAAI,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC;YACtD,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,6BAA6B,MAAM,CAAC,IAAI,GAAG,CAAC,CAAC;YAC7D,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YAC9B,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,KAAK,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC;YACxE,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,MAAM,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,EAAE,CAAC;YAC1C,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,4BAA4B,MAAM,KAAK,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YACtE,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACnD,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAEO,iBAAiB,CAAC,OAAO,EAAE,MAAqB;QACpD,IAAI,MAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,CAAC;YACxC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,6CAA6C,MAAM,KAAK,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YACvF,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,IAAI,MAAM,CAAC,KAAK,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC;YACtG,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;gBACf,OAAO,CAAC,GAAG,CAAC,+CAA+C,MAAM,KAAK,MAAM,CAAC,GAAG,GAAG,CAAC,CAAC;YACzF,CAAC,CAAC,CAAC;YAEH,OAAO,KAAK,CAAC;QACjB,CAAC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;CACJ;AAxDD,uCAwDC"}