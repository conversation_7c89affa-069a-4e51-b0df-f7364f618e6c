{"name": "google-logging-utils", "version": "0.0.2", "description": "An ad-hoc debug logger package for other libraries", "main": "build/src/index.js", "files": ["build/src"], "scripts": {"pretest": "npm run prepare", "test": "c8 mocha build/test", "lint": "gts check test src samples", "clean": "gts clean", "compile": "tsc -p . && cp -r test/fixtures build/test", "fix": "gts fix", "prepare": "npm run compile", "precompile": "gts clean", "samples-test": "echo no samples 🙀", "system-test": "echo no system tests 🙀"}, "author": "Google API Authors", "license": "Apache-2.0", "repository": {"type": "git", "url": "https://github.com/googleapis/gax-nodejs.git", "directory": "logging-utils"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.9.1", "c8": "^9.0.0", "gts": "^5.0.0", "mocha": "^9.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=14"}}