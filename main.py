from flask import Flask, request, jsonify
import os
import json
from together import Together
# Google GenAI import removed
import requests
from bs4 import BeautifulSoup
import time
from requests.adapters import HTT<PERSON><PERSON>pter
from urllib3.util.retry import Retry
from dotenv import load_dotenv
from datetime import datetime, timedelta
import hashlib
import sqlite3
from threading import Thread
import schedule
import re
import random

# Load environment variables
load_dotenv()

app = Flask(__name__)

# Multiple Google Generative AI API configurations
GOOGLE_GENAI_APIS = [
    # All Gemini APIs removed
]

# Multiple Together AI API configurations with higher priority numbers (fallback)
TOGETHER_APIS = [
    {
        'api_key': '728122776d61611edc84ceb621686b3bb9258ed391a84f9d13d0412ab0874e1b',
        'name': 'Primary API',
        'priority': 1,
        'rate_limit': 60,  # requests per minute
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'daily_quota': 500,
        'used_today': 0
    },
    {
        'api_key': '8d39fedf0f485c168b1f5680a9674ccb1407b19d5012b08c64a8f951a9d2d472',
        'name': 'Secondary API',
        'priority': 2,
        'rate_limit': 50,
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'daily_quota': 400,
        'used_today': 0
    },
    {
        'api_key': '5f2c83089545b23ebdc7f2e40ba41250a4b51eb6902a03707aef459b80776e79',
        'name': 'Backup API',
        'priority': 3,
        'rate_limit': 40,
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'daily_quota': 300,
        'used_today': 0
    },
    {
        'api_key': '5a0133c7fdf436d22f6d46670e28740811fc8967e4c0c115387bf4d36e6210d7',
        'name': 'Backup API',
        'priority': 4,
        'rate_limit': 40,
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'daily_quota': 300,
        'used_today': 0
    },
    {
        'api_key': '75038b994a7c45795b03c9528d7c34da186c9d24a73d48cfd44e765c0579b6b7',
        'name': 'Backup API',
        'priority': 5,
        'rate_limit': 40,
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'daily_quota': 300,
        'used_today': 0
    }
]

# Additional fallback APIs (OpenRouter, Anthropic, etc.)
FALLBACK_APIS = [
    {
        'api_key': os.environ.get('OPENROUTER_API_KEY'),
        'name': 'OpenRouter Fallback',
        'priority': 8,
        'rate_limit': 30,
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'provider_type': 'openrouter',
        'daily_quota': 200,
        'used_today': 0
    },
    {
        'api_key': os.environ.get('ANTHROPIC_API_KEY'),
        'name': 'Anthropic Fallback',
        'priority': 9,
        'rate_limit': 40,
        'last_used': 0,
        'error_count': 0,
        'active': True,
        'provider_type': 'anthropic',
        'daily_quota': 300,
        'used_today': 0
    }
]

# Available models for different providers
# Google GenAI models removed

TOGETHER_MODELS = {
    'fast': "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
    'vision': "meta-llama/Llama-Vision-Free", 
    'balanced': "lgai/exaone-3-5-32b-instruct",
    'smart': "lgai/exaone-deep-32b",
    'efficient': "arcee-ai/AFM-4.5B-Preview",
    'creative': "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free"
}

OPENROUTER_MODELS = {
    'fast': "meta-llama/llama-3.1-8b-instruct:free",
    'balanced': "microsoft/wizardlm-2-8x22b",
    'smart': "anthropic/claude-3.5-sonnet",
    'creative': "mistralai/mixtral-8x7b-instruct"
}

class MultiProviderAIManager:
    def __init__(self, google_apis, together_apis, fallback_apis):
        self.google_apis = google_apis
        self.together_apis = together_apis
        self.fallback_apis = fallback_apis
        self.together_clients = {}
        self.openrouter_clients = {}
        self.anthropic_clients = {}
        self.initialize_all_providers()
        self.last_quota_reset = datetime.now().date()
    
    def reset_daily_quotas(self):
        """Reset daily quotas if new day"""
        current_date = datetime.now().date()
        if current_date > self.last_quota_reset:
            print("🔄 Resetting daily quotas...")
            for api in self.google_apis + self.together_apis + self.fallback_apis:
                api['used_today'] = 0
            self.last_quota_reset = current_date
    
    def initialize_all_providers(self):
        """Initialize all AI providers"""
        print("🚀 Initializing all AI providers...")
        
        # Google Generative AI APIs removed - no initialization needed
        
        # Initialize Together AI clients
        for i, config in enumerate(self.together_apis):
            if config['api_key']:
                try:
                    self.together_clients[i] = Together(api_key=config['api_key'])
                    print(f"✅ {config['name']} initialized successfully")
                    config['active'] = True
                except Exception as e:
                    print(f"❌ {config['name']} failed to initialize: {str(e)}")
                    config['active'] = False
            else:
                config['active'] = False
        
        # Initialize fallback providers
        for i, config in enumerate(self.fallback_apis):
            if config['api_key']:
                try:
                    if config['provider_type'] == 'openrouter':
                        self.openrouter_clients[i] = config['api_key']
                        print(f"✅ {config['name']} initialized successfully")
                        config['active'] = True
                    elif config['provider_type'] == 'anthropic':
                        self.anthropic_clients[i] = config['api_key']
                        print(f"✅ {config['name']} initialized successfully")
                        config['active'] = True
                except Exception as e:
                    print(f"❌ {config['name']} failed to initialize: {str(e)}")
                    config['active'] = False
            else:
                config['active'] = False
    
    def get_best_provider(self, model_type='balanced'):
        """Get the best available provider based on priority and health"""
        self.reset_daily_quotas()
        
        all_providers = []

        # Google GenAI providers removed

        # Add Together AI providers
        for i, config in enumerate(self.together_apis):
            if self._is_provider_available(config) and i in self.together_clients:
                all_providers.append(('together', config, i))
        
        # Add fallback providers
        for i, config in enumerate(self.fallback_apis):
            if self._is_provider_available(config):
                all_providers.append((config['provider_type'], config, i))
        
        if not all_providers:
            raise Exception("कोई भी AI provider available नहीं है")
        
        # Sort by priority (lower number = higher priority)
        all_providers.sort(key=lambda x: x[1]['priority'])
        
        # Return the best available provider
        return all_providers[0]
    
    def _is_provider_available(self, config):
        """Check if provider is available for use"""
        if not config['active'] or not config['api_key']:
            return False
        
        # Check error count
        if config['error_count'] >= 5:
            return False
        
        # Check daily quota
        if config['used_today'] >= config['daily_quota']:
            return False
        
        # Check rate limiting
        time_since_last = time.time() - config['last_used']
        min_interval = 60 / config['rate_limit']  # seconds between requests
        
        if time_since_last < min_interval:
            return False
        
        return True
    
    # Google GenAI request method removed - no Google APIs configured
    
    def make_together_request(self, messages, config, client_index, model_type='balanced', max_tokens=200, temperature=0.7):
        """Make request to Together AI"""
        try:
            client = self.together_clients[client_index]
            model = TOGETHER_MODELS.get(model_type, TOGETHER_MODELS['balanced'])
            
            # Update usage tracking
            config['last_used'] = time.time()
            config['used_today'] += 1
            
            print(f"🔄 Using {config['name']} with model: {model}")
            
            response = client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )
            
            # Reset error count on success
            config['error_count'] = 0
            
            return {
                'response': response.choices[0].message.content,
                'api_used': config['name'],
                'model_used': model,
                'provider': 'together',
                'success': True,
                'quota_used': config['used_today'],
                'quota_limit': config['daily_quota']
            }
            
        except Exception as e:
            print(f"❌ {config['name']} failed: {str(e)}")
            config['error_count'] += 1
            
            if config['error_count'] >= 5:
                config['active'] = False
                print(f"🚫 {config['name']} temporarily disabled")
            
            raise e
    
    def make_openrouter_request(self, messages, config, client_index, model_type='balanced', max_tokens=200, temperature=0.7):
        """Make request to OpenRouter"""
        try:
            api_key = self.openrouter_clients[client_index]
            model = OPENROUTER_MODELS.get(model_type, OPENROUTER_MODELS['balanced'])
            
            headers = {
                "Authorization": f"Bearer {api_key}",
                "HTTP-Referer": "http://localhost:5000",
                "X-Title": "Hindi AI Chatbot",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature
            }
            
            # Update usage tracking
            config['last_used'] = time.time()
            config['used_today'] += 1
            
            print(f"🔄 Using {config['name']} with model: {model}")
            
            response = requests.post(
                "https://openrouter.ai/api/v1/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            
            # Reset error count on success
            config['error_count'] = 0
            
            return {
                'response': result['choices'][0]['message']['content'],
                'api_used': config['name'],
                'model_used': model,
                'provider': 'openrouter',
                'success': True,
                'quota_used': config['used_today'],
                'quota_limit': config['daily_quota']
            }
            
        except Exception as e:
            print(f"❌ {config['name']} failed: {str(e)}")
            config['error_count'] += 1
            
            if config['error_count'] >= 5:
                config['active'] = False
                print(f"🚫 {config['name']} temporarily disabled")
            
            raise e
    
    def make_request(self, messages, model_type='balanced', max_tokens=200, temperature=0.7):
        """Make request with automatic provider fallback"""
        last_error = None
        max_attempts = 6  # Try up to 6 different providers
        
        for attempt in range(max_attempts):
            try:
                provider_info = self.get_best_provider(model_type)
                provider_type = provider_info[0]
                config = provider_info[1]
                client_index = provider_info[2]
                
                if provider_type == 'together':
                    return self.make_together_request(messages, config, client_index, model_type, max_tokens, temperature)
                
                elif provider_type == 'openrouter':
                    return self.make_openrouter_request(messages, config, client_index, model_type, max_tokens, temperature)
                
                # Add more provider types as needed
                
            except Exception as e:
                last_error = str(e)
                print(f"🔄 Attempt {attempt + 1}/{max_attempts} failed: {last_error}")
                time.sleep(min(2 ** attempt, 10))  # Exponential backoff
                continue
        
        raise Exception(f"सभी AI providers failed after {max_attempts} attempts: {last_error}")
    
    def get_all_provider_status(self):
        """Get comprehensive status of all providers"""
        self.reset_daily_quotas()
        
        status = {
            'google_genai': [],
            'together_ai': [],
            'fallback_providers': [],
            'summary': {
                'total_providers': len(self.google_apis) + len(self.together_apis) + len(self.fallback_apis),
                'active_providers': 0,
                'total_daily_quota': 0,
                'used_today': 0
            }
        }
        
        # Google GenAI status
        for i, config in enumerate(self.google_apis):
            provider_status = {
                'name': config['name'],
                'active': config['active'],
                'error_count': config['error_count'],
                'priority': config['priority'],
                'rate_limit': config['rate_limit'],
                'daily_quota': config['daily_quota'],
                'used_today': config['used_today'],
                'quota_remaining': config['daily_quota'] - config['used_today'],
                'has_api_key': bool(config['api_key']),
                'available_now': self._is_provider_available(config)
            }
            status['google_genai'].append(provider_status)
            
            if provider_status['active']:
                status['summary']['active_providers'] += 1
            status['summary']['total_daily_quota'] += config['daily_quota']
            status['summary']['used_today'] += config['used_today']
        
        # Together AI status
        for i, config in enumerate(self.together_apis):
            provider_status = {
                'name': config['name'],
                'active': config['active'],
                'error_count': config['error_count'],
                'priority': config['priority'],
                'rate_limit': config['rate_limit'],
                'daily_quota': config['daily_quota'],
                'used_today': config['used_today'],
                'quota_remaining': config['daily_quota'] - config['used_today'],
                'has_client': i in self.together_clients,
                'available_now': self._is_provider_available(config)
            }
            status['together_ai'].append(provider_status)
            
            if provider_status['active']:
                status['summary']['active_providers'] += 1
            status['summary']['total_daily_quota'] += config['daily_quota']
            status['summary']['used_today'] += config['used_today']
        
        # Fallback providers status
        for i, config in enumerate(self.fallback_apis):
            provider_status = {
                'name': config['name'],
                'provider_type': config['provider_type'],
                'active': config['active'],
                'error_count': config['error_count'],
                'priority': config['priority'],
                'rate_limit': config['rate_limit'],
                'daily_quota': config['daily_quota'],
                'used_today': config['used_today'],
                'quota_remaining': config['daily_quota'] - config['used_today'],
                'has_api_key': bool(config['api_key']),
                'available_now': self._is_provider_available(config)
            }
            status['fallback_providers'].append(provider_status)
            
            if provider_status['active']:
                status['summary']['active_providers'] += 1
            status['summary']['total_daily_quota'] += config['daily_quota']
            status['summary']['used_today'] += config['used_today']
        
        return status
    
    def get_load_balancing_stats(self):
        """Get load balancing and usage statistics"""
        stats = {
            'priority_distribution': {},
            'provider_health': {},
            'quota_utilization': {}
        }
        
        all_configs = self.google_apis + self.together_apis + self.fallback_apis
        
        for config in all_configs:
            priority = config['priority']
            if priority not in stats['priority_distribution']:
                stats['priority_distribution'][priority] = []
            
            stats['priority_distribution'][priority].append({
                'name': config['name'],
                'active': config['active'],
                'used_today': config['used_today']
            })
            
            # Provider health score (0-100)
            health_score = 100
            if config['error_count'] > 0:
                health_score -= min(config['error_count'] * 10, 80)
            if not config['active']:
                health_score = 0
            if config['used_today'] >= config['daily_quota']:
                health_score = max(health_score - 50, 0)
            
            stats['provider_health'][config['name']] = health_score
            
            # Quota utilization percentage
            utilization = (config['used_today'] / config['daily_quota']) * 100 if config['daily_quota'] > 0 else 0
            stats['quota_utilization'][config['name']] = round(utilization, 1)
        
        return stats

# Initialize the Multi-Provider AI Manager
ai_manager = MultiProviderAIManager(GOOGLE_GENAI_APIS, TOGETHER_APIS, FALLBACK_APIS)

# Keep all existing utility functions unchanged
session = requests.Session()
retry_strategy = Retry(
    total=3,
    backoff_factor=1,
    status_forcelist=[429, 500, 502, 503, 504],
)
adapter = HTTPAdapter(max_retries=retry_strategy)
session.mount("http://", adapter)
session.mount("https://", adapter)

def filter_profanity(text):
    """Filter inappropriate content and symbols"""
    profanity_pattern = r'[%#&^$!)*_~(]+'
    cleaned_text = re.sub(profanity_pattern, '', text)
    cleaned_text = re.sub(r'\s+', ' ', cleaned_text)
    
    inappropriate_words = ['fuck', 'shit', 'damn', 'hell']
    for word in inappropriate_words:
        cleaned_text = re.sub(word, '***', cleaned_text, flags=re.IGNORECASE)
    
    return cleaned_text.strip()

def make_response_short(text, max_length=150):
    """Make response short and concise"""
    if len(text) <= max_length:
        return text
    
    sentences = text.split('।')
    result = ""
    
    for sentence in sentences:
        if len(result + sentence + '।') <= max_length:
            result += sentence + '।'
        else:
            break
    
    if not result:
        result = text[:max_length-3] + "..."
    
    return result.strip()

def select_model_for_query(query):
    """Select best model based on query type"""
    query_lower = query.lower()
    
    if any(word in query_lower for word in ['hello', 'hi', 'thanks', 'bye', 'नमस्ते', 'धन्यवाद']):
        return 'fast'
    elif any(word in query_lower for word in ['poem', 'story', 'creative', 'कविता', 'कहानी']):
        return 'creative'
    elif any(word in query_lower for word in ['explain', 'analyze', 'compare', 'समझाएं', 'विश्लेषण']):
        return 'smart'
    return 'balanced'

# Database functions with enhanced tracking
def init_cache_db():
    """Initialize SQLite cache database"""
    conn = sqlite3.connect('cache.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS search_cache (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            query_hash TEXT UNIQUE,
            query_type TEXT,
            query TEXT,
            response TEXT,
            sources_data TEXT,
            created_at TIMESTAMP,
            expires_at TIMESTAMP,
            hit_count INTEGER DEFAULT 0,
            api_used TEXT,
            model_used TEXT,
            provider TEXT,
            quota_used INTEGER,
            quota_limit INTEGER
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS api_usage_stats (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            api_name TEXT,
            model_used TEXT,
            provider TEXT,
            query_type TEXT,
            response_time REAL,
            success BOOLEAN,
            quota_used INTEGER,
            quota_limit INTEGER,
            created_at TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS provider_health_log (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            provider_name TEXT,
            provider_type TEXT,
            health_score INTEGER,
            error_count INTEGER,
            active_status BOOLEAN,
            quota_utilization REAL,
            created_at TIMESTAMP
        )
    ''')

    # Migration: Add missing columns to existing api_usage_stats table
    try:
        cursor.execute("ALTER TABLE api_usage_stats ADD COLUMN provider TEXT")
    except sqlite3.OperationalError:
        pass  # Column already exists

    try:
        cursor.execute("ALTER TABLE api_usage_stats ADD COLUMN quota_used INTEGER DEFAULT 0")
    except sqlite3.OperationalError:
        pass  # Column already exists

    try:
        cursor.execute("ALTER TABLE api_usage_stats ADD COLUMN quota_limit INTEGER DEFAULT 0")
    except sqlite3.OperationalError:
        pass  # Column already exists

    conn.commit()
    conn.close()

init_cache_db()

def log_api_usage(api_name, model_used, provider, query_type, response_time, success, quota_used=0, quota_limit=0):
    """Enhanced API usage logging"""
    conn = sqlite3.connect('cache.db')
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO api_usage_stats 
        (api_name, model_used, provider, query_type, response_time, success, quota_used, quota_limit, created_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (api_name, model_used, provider, query_type, response_time, success, quota_used, quota_limit, datetime.now()))
    
    conn.commit()
    conn.close()

def log_provider_health():
    """Log provider health with error handling"""
    try:
        stats = ai_manager.get_load_balancing_stats()

        conn = sqlite3.connect('cache.db')
        cursor = conn.cursor()

        for provider_name, health_score in stats['provider_health'].items():
            # Find the config for this provider
            config = None
            for c in ai_manager.google_apis + ai_manager.together_apis + ai_manager.fallback_apis:
                if c['name'] == provider_name:
                    config = c
                    break

            if config:
                # Debug: Check what keys are available
                if 'used_today' not in config:
                    print(f"❌ Missing 'used_today' in {provider_name}: {list(config.keys())}")
                    continue
                if 'daily_quota' not in config:
                    print(f"❌ Missing 'daily_quota' in {provider_name}: {list(config.keys())}")
                    continue

                quota_utilization = stats['quota_utilization'].get(provider_name, 0)

                cursor.execute('''
                    INSERT INTO provider_health_log
                    (provider_name, provider_type, health_score, error_count, active_status, quota_utilization, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (provider_name,
                      config.get('provider_type', 'google' if 'Google' in provider_name else 'together'),
                      health_score,
                      config.get('error_count', 0),
                      config.get('active', False),
                      quota_utilization,
                      datetime.now()))
            else:
                print(f"❌ Config not found for provider: {provider_name}")

        conn.commit()
        conn.close()
    except Exception as e:
        print(f"❌ Error logging provider health: {e}")
        import traceback
        traceback.print_exc()

# Web search function (unchanged)
def web_search(query, num_results=3):
    """Google Custom Search API function"""
    base_url = "https://www.googleapis.com/customsearch/v1"
    
    api_key = os.environ.get('GOOGLE_API_KEY')
    search_engine_id = os.environ.get('GOOGLE_SEARCH_ENGINE_ID')
    
    params = {
        'key': api_key,
        'cx': search_engine_id,
        'q': query,
        'num': min(num_results, 10)
    }
    
    try:
        response = requests.get(base_url, params=params, timeout=15)
        response.raise_for_status()
        data = response.json()
        
        formatted_results = {"organic": []}
        
        if "items" in data:
            for item in data["items"]:
                formatted_results["organic"].append({
                    "title": item.get("title", ""),
                    "snippet": item.get("snippet", ""),
                    "link": item.get("link", "")
                })
        
        return formatted_results
        
    except Exception as e:
        return {"error": f"Search error: {str(e)}"}

@app.route('/chat', methods=['POST'])
def unified_chat():
    """Enhanced Hindi chat with multiple AI providers and intelligent fallback"""
    data = request.get_json()
    
    if not data or 'message' not in data:
        return jsonify({'error': 'Message नहीं मिला'}), 400

    try:
        message = data['message']
        use_crawling = data.get('crawl_websites', True)
        
        # Filter input message
        message = filter_profanity(message)
        
        # Select best model for this query
        model_type = select_model_for_query(message)
        
        start_time = time.time()
        
        # Check if search is needed using the multi-provider manager
        search_check_messages = [{"role": "user", "content": f"""
        User query: "{message}"
        
        क्या इस सवाल के लिए current/real-time information चाहिए?
        Consider: news, weather, prices, current events, live data
        
        Reply only: YES or NO
        """}]
        
        search_decision_result = ai_manager.make_request(
            messages=search_check_messages,
            model_type='fast',
            max_tokens=5,
            temperature=0.1
        )
        
        needs_search = "YES" in search_decision_result['response'].upper()
        
        if needs_search:
            # Generate search query
            search_query_messages = [{"role": "user", "content": f"""
            User asked: "{message}"
            
            Create a focused English search query to find current information.
            Return only the search query, nothing else.
            """}]
            
            query_result = ai_manager.make_request(
                messages=search_query_messages,
                model_type='fast',
                max_tokens=50,
                temperature=0.3
            )
            
            search_query = query_result['response'].strip().strip('"\'')
            
            # Perform web search
            search_results = web_search(search_query, num_results=3)
            
            if "error" in search_results:
                return jsonify({
                    'response': "मुझे current information नहीं मिल सका। कृपया बाद में try करें।",
                    'used_search': False,
                    'error': search_results['error'],
                    'api_used': query_result['api_used'],
                    'provider': query_result['provider']
                })
            
            # Build context from search results
            search_context = ""
            if "organic" in search_results and search_results["organic"]:
                search_context = "=== Search Results ===\n"
                for i, result in enumerate(search_results["organic"][:3], 1):
                    search_context += f"Source {i}:\n"
                    search_context += f"Title: {result.get('title', '')}\n"
                    search_context += f"Summary: {result.get('snippet', '')}\n\n"
            
            if not search_context.strip():
                return jsonify({
                    'response': "उस topic के बारे में current information नहीं मिली।",
                    'used_search': True,
                    'api_used': query_result['api_used'],
                    'provider': query_result['provider']
                })
            
            # Generate Hindi response
            final_messages = [{"role": "user", "content": f"""
            User question: {message}
            
            Information found:
            {search_context}
            
            Instructions:
            1. जवाब हिंदी में दें (Answer in Hindi)
            2. बहुत छोटा और स्पष्ट उत्तर दें (Very short and clear answer)
            3. केवल 1-2 वाक्यों में जवाब दें (Only 1-2 sentences)
            4. मुख्य बात बताएं (State the main point)
            5. अनुचित भाषा का प्रयोग न करें (No inappropriate language)
            6. Numbers, dates, prices को clearly mention करें
            """}]
            
            response_result = ai_manager.make_request(
                messages=final_messages,
                model_type=model_type,
                max_tokens=200,
                temperature=0.7
            )
            
            final_response = response_result['response']
            final_response = filter_profanity(final_response)
            final_response = make_response_short(final_response, 150)
            
            response_time = time.time() - start_time
            
            # Enhanced logging
            log_api_usage(
                response_result['api_used'], 
                response_result['model_used'], 
                response_result['provider'],
                'search_based', 
                response_time, 
                True,
                response_result.get('quota_used', 0),
                response_result.get('quota_limit', 0)
            )
            
            return jsonify({
                'response': final_response,
                'used_search': True,
                'search_query': search_query,
                'model_type': model_type,
                'api_used': response_result['api_used'],
                'model_used': response_result['model_used'],
                'provider': response_result['provider'],
                'quota_info': {
                    'used': response_result.get('quota_used', 0),
                    'limit': response_result.get('quota_limit', 0),
                    'remaining': response_result.get('quota_limit', 0) - response_result.get('quota_used', 0)
                },
                'response_time': round(response_time, 2)
            })
        
        else:
            # Direct Hindi response without search
            direct_messages = [{"role": "user", "content": f"""
            User: {message}
            
            निर्देश:
            - हिंदी में जवाब दें
            - बहुत छोटा उत्तर दें (1-2 sentences)
            - सरल और समझने योग्य भाषा में
            - अनुचित शब्दों का प्रयोग न करें
            - मददगार रहें
            """}]
            
            response_result = ai_manager.make_request(
                messages=direct_messages,
                model_type=model_type,
                max_tokens=150,
                temperature=0.8
            )
            
            final_response = response_result['response']
            final_response = filter_profanity(final_response)
            final_response = make_response_short(final_response, 150)
            
            response_time = time.time() - start_time
            
            # Enhanced logging
            log_api_usage(
                response_result['api_used'], 
                response_result['model_used'], 
                response_result['provider'],
                'direct', 
                response_time, 
                True,
                response_result.get('quota_used', 0),
                response_result.get('quota_limit', 0)
            )
            
            return jsonify({
                'response': final_response,
                'used_search': False,
                'model_type': model_type,
                'api_used': response_result['api_used'],
                'model_used': response_result['model_used'],
                'provider': response_result['provider'],
                'quota_info': {
                    'used': response_result.get('quota_used', 0),
                    'limit': response_result.get('quota_limit', 0),
                    'remaining': response_result.get('quota_limit', 0) - response_result.get('quota_used', 0)
                },
                'response_time': round(response_time, 2)
            })
            
    except Exception as e:
        response_time = time.time() - start_time if 'start_time' in locals() else 0
        
        # Log failed attempt
        log_api_usage('failed', 'unknown', 'unknown', 'error', response_time, False)
        
        return jsonify({'error': f'Server error: {str(e)}'}), 500

# Enhanced API endpoints
@app.route('/api/status', methods=['GET'])
def api_status():
    """Comprehensive status of all AI providers"""
    status = ai_manager.get_all_provider_status()
    load_stats = ai_manager.get_load_balancing_stats()
    
    return jsonify({
        'providers': status,
        'load_balancing': load_stats,
        'available_models': {
            'together_ai': TOGETHER_MODELS,
            'openrouter': OPENROUTER_MODELS
        },
        'summary': status['summary']
    })

@app.route('/api/stats', methods=['GET'])
def api_usage_stats():
    """Enhanced API usage statistics with provider breakdown"""
    conn = sqlite3.connect('cache.db')
    cursor = conn.cursor()
    
    # Get usage stats by provider and API (last 24 hours)
    cursor.execute('''
        SELECT provider, api_name, COUNT(*) as requests, AVG(response_time) as avg_time,
               SUM(CASE WHEN success THEN 1 ELSE 0 END) as successful,
               AVG(quota_used) as avg_quota_used, MAX(quota_limit) as quota_limit
        FROM api_usage_stats 
        WHERE created_at > datetime('now', '-24 hours')
        GROUP BY provider, api_name
        ORDER BY provider, requests DESC
    ''')
    
    provider_stats = {}
    total_requests = 0
    total_successful = 0
    
    for row in cursor.fetchall():
        provider = row[0] or 'unknown'
        if provider not in provider_stats:
            provider_stats[provider] = []
        
        requests = row[2]
        successful = row[4]
        total_requests += requests
        total_successful += successful
        
        provider_stats[provider].append({
            'api_name': row[1],
            'total_requests': requests,
            'avg_response_time': round(row[3], 2) if row[3] else 0,
            'successful_requests': successful,
            'success_rate': round((successful / requests) * 100, 1) if requests > 0 else 0,
            'avg_quota_used': round(row[5], 1) if row[5] else 0,
            'quota_limit': row[6] or 0
        })
    
    # Get hourly distribution
    cursor.execute('''
        SELECT strftime('%H', created_at) as hour, provider, COUNT(*) as requests
        FROM api_usage_stats 
        WHERE created_at > datetime('now', '-24 hours')
        GROUP BY hour, provider
        ORDER BY hour, provider
    ''')
    
    hourly_distribution = {}
    for row in cursor.fetchall():
        hour = f"{row[0]}:00"
        provider = row[1] or 'unknown'
        if hour not in hourly_distribution:
            hourly_distribution[hour] = {}
        hourly_distribution[hour][provider] = row[2]
    
    conn.close()
    
    return jsonify({
        'last_24_hours': provider_stats,
        'hourly_distribution': hourly_distribution,
        'summary': {
            'total_requests': total_requests,
            'total_successful': total_successful,
            'overall_success_rate': round((total_successful / total_requests) * 100, 1) if total_requests > 0 else 0
        },
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/health-logs', methods=['GET'])
def provider_health_logs():
    """Get provider health logs"""
    conn = sqlite3.connect('cache.db')
    cursor = conn.cursor()
    
    hours = request.args.get('hours', 24, type=int)
    
    cursor.execute('''
        SELECT provider_name, provider_type, health_score, error_count, 
               active_status, quota_utilization, created_at
        FROM provider_health_log 
        WHERE created_at > datetime('now', '-{} hours')
        ORDER BY created_at DESC
        LIMIT 1000
    '''.format(hours))
    
    health_logs = []
    for row in cursor.fetchall():
        health_logs.append({
            'provider_name': row[0],
            'provider_type': row[1],
            'health_score': row[2],
            'error_count': row[3],
            'active_status': bool(row[4]),
            'quota_utilization': row[5],
            'timestamp': row[6]
        })
    
    conn.close()
    
    return jsonify({
        'health_logs': health_logs,
        'period_hours': hours,
        'total_entries': len(health_logs)
    })

@app.route('/api/reset', methods=['POST'])
def reset_apis():
    """Reset all provider error counts and reactivate"""
    reset_type = request.json.get('type', 'all') if request.json else 'all'
    
    if reset_type == 'all' or reset_type == 'google':
        for config in GOOGLE_GENAI_APIS:
            config['error_count'] = 0
            config['active'] = bool(config['api_key'])
    
    if reset_type == 'all' or reset_type == 'together':
        for config in TOGETHER_APIS:
            config['error_count'] = 0
            config['active'] = bool(config['api_key'])
    
    if reset_type == 'all' or reset_type == 'fallback':
        for config in FALLBACK_APIS:
            config['error_count'] = 0
            config['active'] = bool(config['api_key'])
    
    # Reset quotas if requested
    if request.json and request.json.get('reset_quotas', False):
        for config in GOOGLE_GENAI_APIS + TOGETHER_APIS + FALLBACK_APIS:
            config['used_today'] = 0
    
    # Reinitialize all providers
    ai_manager.initialize_all_providers()
    
    status = ai_manager.get_all_provider_status()
    
    return jsonify({
        'message': f'सभी AI providers reset हो गए ({reset_type})',
        'active_providers': status['summary']['active_providers'],
        'reset_type': reset_type
    })

@app.route('/api/quota-reset', methods=['POST'])
def reset_quotas():
    """Reset daily quotas for all providers"""
    for config in GOOGLE_GENAI_APIS + TOGETHER_APIS + FALLBACK_APIS:
        config['used_today'] = 0
    
    return jsonify({
        'message': 'सभी daily quotas reset हो गए',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health', methods=['GET'])
def health_check():
    """Comprehensive health check"""
    status = ai_manager.get_all_provider_status()
    load_stats = ai_manager.get_load_balancing_stats()
    
    # Log current provider health
    log_provider_health()
    
    # Calculate overall health score
    total_providers = status['summary']['total_providers']
    active_providers = status['summary']['active_providers']
    health_percentage = (active_providers / total_providers) * 100 if total_providers > 0 else 0
    
    # Determine status
    if health_percentage >= 80:
        overall_status = 'healthy'
    elif health_percentage >= 50:
        overall_status = 'degraded'
    else:
        overall_status = 'critical'
    
    return jsonify({
        'status': overall_status,
        'health_percentage': round(health_percentage, 1),
        'message': f'Multi-Provider Hindi AI API: {active_providers}/{total_providers} providers active',
        'providers_summary': {
            'google_genai': len([p for p in status['google_genai'] if p['active']]),
            'together_ai': len([p for p in status['together_ai'] if p['active']]),
            'fallback_providers': len([p for p in status['fallback_providers'] if p['active']])
        },
        'quota_summary': {
            'total_quota': status['summary']['total_daily_quota'],
            'used_today': status['summary']['used_today'],
            'remaining': status['summary']['total_daily_quota'] - status['summary']['used_today']
        },
        'load_balancing': load_stats
    })

@app.route('/', methods=['GET'])
def home():
    """Enhanced home page with comprehensive provider information"""
    status = ai_manager.get_all_provider_status()
    
    return jsonify({
        'message': 'Multi-Provider Hindi AI Chatbot with Intelligent Fallback',
        'version': '2.0',
        'features': {
            'multiple_google_apis': f"{len(GOOGLE_GENAI_APIS)} Google Generative AI APIs",
            'multiple_together_apis': f"{len(TOGETHER_APIS)} Together AI APIs",
            'fallback_providers': f"{len(FALLBACK_APIS)} additional fallback providers",
            'intelligent_priority': 'Google GenAI → Together AI → Fallback providers',
            'quota_management': 'Daily quota tracking and automatic rotation',
            'health_monitoring': 'Real-time provider health scoring',
            'load_balancing': 'Intelligent load distribution across providers',
            'hindi_responses': 'All responses in Hindi with content filtering',
            'comprehensive_analytics': 'Detailed usage, health, and performance metrics'
        },
        'providers_status': {
            'total_providers': status['summary']['total_providers'],
            'active_providers': status['summary']['active_providers'],
            'google_genai_active': len([p for p in status['google_genai'] if p['active']]),
            'together_ai_active': len([p for p in status['together_ai'] if p['active']]),
            'fallback_active': len([p for p in status['fallback_providers'] if p['active']])
        },
        'quota_info': {
            'total_daily_quota': status['summary']['total_daily_quota'],
            'used_today': status['summary']['used_today'],
            'remaining_quota': status['summary']['total_daily_quota'] - status['summary']['used_today']
        },
        'endpoints': {
            '/chat': 'POST - Chat with multi-provider AI support',
            '/api/status': 'GET - Comprehensive provider status',
            '/api/stats': 'GET - Usage statistics by provider',
            '/api/health-logs': 'GET - Provider health history',
            '/api/reset': 'POST - Reset provider error counts',
            '/api/quota-reset': 'POST - Reset daily quotas',
            '/health': 'GET - Overall system health'
        }
    })

# Background task to log provider health periodically
def background_health_logging():
    """Background task to log provider health every 10 minutes"""
    while True:
        try:
            log_provider_health()
            print("📊 Provider health logged")
        except Exception as e:
            print(f"❌ Error logging provider health: {e}")
        time.sleep(600)  # 10 minutes

# Start background health logging
health_thread = Thread(target=background_health_logging, daemon=True)
health_thread.start()

if __name__ == '__main__':
    print("🚀 Multi-Provider Hindi AI Chatbot starting...")
    print(f"📊 {len(GOOGLE_GENAI_APIS)} Google GenAI APIs + {len(TOGETHER_APIS)} Together AI APIs + {len(FALLBACK_APIS)} Fallback APIs")
    print("🎯 Priority Order: Google GenAI → Together AI → Fallback Providers")
    print("💡 Features: Intelligent fallback, quota management, health monitoring, load balancing")
    app.run(debug=True, host='0.0.0.0', port=5000)
