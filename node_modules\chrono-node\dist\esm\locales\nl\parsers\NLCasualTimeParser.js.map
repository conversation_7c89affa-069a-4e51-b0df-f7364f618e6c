{"version": 3, "file": "NLCasualTimeParser.js", "sourceRoot": "", "sources": ["../../../../../src/locales/nl/parsers/NLCasualTimeParser.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,sCAAsC,EAAE,MAAM,wDAAwD,CAAC;AAChH,OAAO,KAAK,MAAM,OAAO,CAAC;AAC1B,OAAO,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AAExD,MAAM,SAAS,GAAG,CAAC,CAAC;AACpB,MAAM,YAAY,GAAG,CAAC,CAAC;AAEvB,MAAM,CAAC,OAAO,OAAO,kBAAmB,SAAQ,sCAAsC;IAClF,YAAY;QACR,OAAO,iGAAiG,CAAC;IAC7G,CAAC;IAED,YAAY,CAAC,OAAuB,EAAE,KAAuB;QACzD,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAC1C,MAAM,SAAS,GAAG,OAAO,CAAC,uBAAuB,EAAE,CAAC;QAEpD,IAAI,KAAK,CAAC,SAAS,CAAC,KAAK,MAAM,EAAE,CAAC;YAC9B,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;YACnD,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YAC1D,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC;QAC5D,CAAC;QAED,QAAQ,KAAK,CAAC,YAAY,CAAC,CAAC,WAAW,EAAE,EAAE,CAAC;YACxC,KAAK,UAAU,CAAC;YAChB,KAAK,cAAc;gBACf,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,MAAM;YAEV,KAAK,OAAO,CAAC;YACb,KAAK,YAAY;gBACb,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,MAAM;YAEV,KAAK,aAAa;gBACd,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACxC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAC3B,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAC7B,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC,CAAC,CAAC;gBAC7B,MAAM;YAEV,KAAK,SAAS,CAAC;YACf,KAAK,aAAa;gBACd,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAC3B,MAAM;YAEV,KAAK,QAAQ,CAAC;YACd,KAAK,YAAY;gBACb,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,MAAM;QACd,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ"}