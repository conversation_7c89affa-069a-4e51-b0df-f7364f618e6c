import { mysqlConnection } from "../config/mysql.js";
import { redisClient } from "../config/redis.js";

export function checkMemoryRequest(message) {
  const memoryKeywords = [
    "yaad rakhna", "yaad rakho", "remember this", "save this",
    "yaad rakh", "store this", "memory mein rakh", "note kar lo",
    "samjha", "yaad hai", "remember kar", "save kar"
  ];
  return memoryKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

export async function saveMemory(userId, keyName, value) {
  try {
    await mysqlConnection.execute(
      "INSERT INTO memory (user_id, key_name, value) VALUES (?, ?, ?)",
      [userId, keyName, value]
    );
    return true;
  } catch (error) {
    console.error("Error saving memory:", error);
    return false;
  }
}

export async function getUserMemories(userId) {
  try {
    const [rows] = await mysqlConnection.execute(
      "SELECT key_name, value FROM memory WHERE user_id = ? OR<PERSON><PERSON> BY created_at DESC LIMIT 20",
      [userId]
    );
    return rows;
  } catch (error) {
    console.error("Error fetching memories:", error);
    return [];
  }
}

export async function extractMemoryInfo(message) {
  // Minimal implementation here; replace with actual LLM call if needed
  const key = "general_info";
  const value = message.replace(/yaad rakh(na|o)?|remember this|save this/gi, "").trim();
  return { key, value };
}

export function checkTodoRequest(message) {
  const todoKeywords = [
    "todo bana do", "todo banao", "add todo", "create todo", "todo banana hai", "todo", "task bana do", "task banao", "add task", "remind me to", "yaad dilana", "reminder set karo"
  ];
  return todoKeywords.some(keyword => message.toLowerCase().includes(keyword));
}

export async function saveTodo(userId, todoTitle, todoDetails) {
  try {
    const todo = {
      title: todoTitle,
      details: todoDetails,
      created_at: new Date().toISOString()
    };
    await redisClient.lPush(`todos:${userId}`, JSON.stringify(todo));
    return true;
  } catch (error) {
    console.error("Error saving todo (Redis):", error);
    return false;
  }
}

export async function getUserTodos(userId, limit = 20) {
  try {
    const todos = await redisClient.lRange(`todos:${userId}`, 0, limit - 1);
    return todos.map(t => JSON.parse(t));
  } catch (error) {
    console.error("Error fetching todos (Redis):", error);
    return [];
  }
}

export async function extractTodoInfo(message) {
  // Minimal implementation; can be improved with LLM
  // Extracts the todo title and details from the message
  let title = "General Todo";
  let details = message.replace(/todo bana do|todo banao|add todo|create todo|todo banana hai|todo|task bana do|task banao|add task|remind me to|yaad dilana|reminder set karo/gi, "").trim();
  if (details.length > 0) title = details;
  return { title, details };
}
