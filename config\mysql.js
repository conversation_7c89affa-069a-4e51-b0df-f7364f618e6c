import mysql from "mysql2/promise";
import dotenv from "dotenv";
dotenv.config();

export const mysqlConnection = await mysql.createConnection({
  host: process.env.MYSQL_HOST || 'mercury.nityasha.com',
  user: process.env.MYSQL_USER || 'kzzuezbs_31aa9913123139jmasr',
  password: process.env.MYSQL_PASSWORD || 'N4(I9_P9>!lPo:vmT0',
  database: process.env.MYSQL_DATABASE || 'kzzuezbs_31aa9913123139jmasr'
});

// Export config object for use with createConnection elsewhere
export const mysqlConfig = {
  host: process.env.MYSQL_HOST || 'mercury.nityasha.com',
  user: process.env.MYSQL_USER || 'kzzuezbs_31aa9913123139jmasr',
  password: process.env.MYSQL_PASSWORD || 'N4(I9_P9>!lPo:vmT0',
  database: process.env.MYSQL_DATABASE || 'kzzuezbs_31aa9913123139jmasr'
};
