{"name": "expo-server-sdk", "version": "3.15.0", "description": "Server-side library for working with Expo using Node.js", "main": "build/ExpoClient.js", "types": "build/ExpoClient.d.ts", "files": ["build"], "scripts": {"build": "tsc --project tsconfig.build.json", "lint": "eslint src", "prepare": "yarn build", "test": "jest", "tsc": "tsc", "watch": "tsc --watch"}, "eslintConfig": {"extends": "universe/node"}, "jest": {"coverageDirectory": "<rootDir>/../coverage", "coverageThreshold": {"global": {"branches": 100, "functions": 100, "lines": 100, "statements": 0}}, "preset": "ts-jest", "rootDir": "src", "testEnvironment": "node"}, "repository": {"type": "git", "url": "git+https://github.com/expo/expo-server-sdk-node.git"}, "keywords": ["expo", "push-notifications"], "author": "<EMAIL>", "license": "MIT", "bugs": {"url": "https://github.com/expo/expo-server-sdk-node/issues"}, "homepage": "https://github.com/expo/expo-server-sdk-node#readme", "dependencies": {"node-fetch": "^2.6.0", "promise-limit": "^2.7.0", "promise-retry": "^2.0.1"}, "devDependencies": {"@tsconfig/node-lts": "22.0.1", "@tsconfig/strictest": "2.0.5", "@types/node-fetch": "2.6.12", "@types/promise-retry": "1.1.6", "eslint": "^8.57.0", "eslint-config-universe": "14.0.0", "jest": "29.7.0", "msw": "2.7.3", "prettier": "3.5.3", "ts-jest": "29.3.2", "typescript": "5.8.3"}, "packageManager": "yarn@4.9.1"}