import { <PERSON><PERSON><PERSON> } from "jsdom";
import { Readability } from "@mozilla/readability";
import axios from "axios";
import * as cheerio from "cheerio";
import puppeteer from "puppeteer";
import mysql from 'mysql2/promise';
import { mysqlConfig } from '../config/mysql.js';

export async function extractReadableContent(url) {
  try {
    const { data: html } = await axios.get(url);
    const dom = new JSDOM(html, { url });
    const reader = new Readability(dom.window.document);
    const article = reader.parse();
    return article?.textContent?.slice(0, 500) + "...";
  } catch {
    return "";
  }
}

export function getFaviconURL(link) {
  try {
    const url = new URL(link);
    return `https://www.google.com/s2/favicons?domain=${url.hostname}&sz=64`;
  } catch {
    return "";
  }
}

export function getWebsiteName(link) {
  try {
    const url = new URL(link);
    const domain = url.hostname.replace("www.", "").split(".")[0];
    return domain.charAt(0).toUpperCase() + domain.slice(1);
  } catch {
    return "";
  }
}

export async function fetchWeatherFromOpenWeather(city = "Delhi") {
  const apiKey = process.env.OPENWEATHER_API_KEY || '********************************';
  if (!apiKey) return null;
  try {
    const url = `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(city)}&appid=${apiKey}&units=metric&lang=hi`;
    const { data } = await axios.get(url);
    return {
      city: data.name,
      temp: data.main.temp,
      desc: data.weather[0].description,
      humidity: data.main.humidity,
      wind: data.wind.speed,
      icon: data.weather[0].icon
    };
  } catch (err) {
    return null;
  }
}

// Extract main keywords from a question (simple version)
export function extractKeywords(question) {
  // Remove stopwords and non-alphabetic chars, return top words
  const stopwords = ["what", "who", "is", "the", "a", "an", "in", "on", "of", "for", "to", "and", "or", "ka", "ki", "ke", "hai", "hai?", "kya", "kaun", "kaise", "kab", "kahan", "ka", "mein", "par", "se", "ko", "tha", "thi", "tha?", "ho", "ho?", "hai.", "hai:"];
  return question
    .toLowerCase()
    .replace(/[^\w\s]/g, " ")
    .split(/\s+/)
    .filter(word => word && !stopwords.includes(word))
    .slice(0, 5); // top 5 keywords
}

// Crawl and summarize main content from a URL
export async function crawlAndSummarize(url) {
  try {
    const { data: html } = await axios.get(url, { timeout: 7000 });
    const $ = cheerio.load(html);
    // Try to get main content from <article>, <main>, or <body>
    let text = $("article").text() || $("main").text() || $("body").text();
    text = text.replace(/\s+/g, " ").trim();
    // Return first 500 chars as summary
    return text.slice(0, 500) + (text.length > 500 ? "..." : "");
  } catch (e) {
    return "";
  }
}

// Scrape a page for an Indian phone number
export async function scrapePageForPhoneNumber(url) {
  try {
    const { data: html } = await axios.get(url, { timeout: 10000 });
    const $ = cheerio.load(html);
    // Get all text content
    const text = $('body').text();
    // Regex for Indian phone numbers (10 digits, may have +91, spaces, or dashes)
    const match = text.match(/(?:\+91[-\s]?)?[6-9]\d{9}/g);
    return match ? match[0] : null;
  } catch (err) {
    console.error('Failed to scrape', url, err.message);
    return null;
  }
}

// Scrape a page for an Indian phone number using Puppeteer (for JS-heavy sites)
export async function scrapePageForPhoneNumberPuppeteer(url) {
  let browser;
  try {
    browser = await puppeteer.launch({ headless: "new", args: ["--no-sandbox", "--disable-setuid-sandbox"] });
    const page = await browser.newPage();
    await page.goto(url, { waitUntil: "networkidle2", timeout: 30000 });
    // Get all visible text
    const text = await page.evaluate(() => document.body.innerText);
    // Regex for Indian phone numbers (10 digits, may have +91, spaces, or dashes)
    const match = text.match(/(?:\+91[-\s]?)?[6-9]\d{9}/g);
    await browser.close();
    return match ? match[0] : null;
  } catch (err) {
    if (browser) await browser.close();
    console.error('Puppeteer scrape failed', url, err.message);
    return null;
  }
}

// Improved: Search the info table for any matching entry using FULLTEXT, fallback to keywords
export async function searchInfoTable(query) {
  const connection = await mysql.createConnection(mysqlConfig);
  try {
    // Try FULLTEXT search first
    let [rows] = await connection.execute(
      `SELECT *, MATCH(name, about, tags, city, address) AGAINST (? IN NATURAL LANGUAGE MODE) AS score
       FROM info
       WHERE MATCH(name, about, tags, city, address) AGAINST (? IN NATURAL LANGUAGE MODE)
       ORDER BY score DESC
       LIMIT 5`,
      [query, query]
    );
    if (rows.length > 0) return rows;
    // Fallback: keyword-based LIKE search
    const keywords = query.split(/\s+/).filter(w => w.length > 2);
    if (keywords.length === 0) return [];
    const where = keywords.map(() =>
      '(name LIKE ? OR address LIKE ? OR about LIKE ? OR city LIKE ? OR tags LIKE ? OR number LIKE ?)'
    ).join(' OR ');
    const params = [];
    for (const kw of keywords) {
      for (let i = 0; i < 6; i++) params.push(`%${kw}%`);
    }
    [rows] = await connection.execute(
      `SELECT * FROM info WHERE ${where} LIMIT 5`,
      params
    );
    console.log('MySQL search keywords:', keywords, 'Results:', rows);
    return rows;
  } finally {
    await connection.end();
  }
}
