import express from "express";
import {
  checkMemoryRequest,
  extractMemoryInfo,
  saveMemory,
  getUserMemories,
} from "../utils/memory.js";
import { getChatHistory, addToChatHistory } from "../utils/history.js";
import Redis from "redis";
import { runGeminiAgent, runTogetherAgent } from "../utils/aiAgent.js";
import { tools } from "../utils/tools.js";
import db from '../utils/db.js'; // at the top if not already

const router = express.Router();

// Initialize Redis client
const redis = Redis.createClient({
  socket: {
    host: "mercury.nityasha.com",
    port: 26739,
  },
  password: "Amber@!23",
});

redis.on("error", (err) => console.log("Redis Client Error", err));
redis.connect().catch(console.error);

// Remove emojis from response
function removeEmojis(text) {
  if (typeof text !== 'string') text = String(text ?? '');
  return text.replace(/[😀-🙏]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu, "");
}

// Remove special characters from response (keep Hindi, English, numbers, basic punctuation)
function removeSpecialChars(text) {
  // This regex keeps Hindi (\u0900-\u097F), English (a-zA-Z), numbers, spaces, and basic punctuation
  return text.replace(/[^\u0900-\u097Fa-zA-Z0-9 .,!?-@#'"\n]/g, "");
}

// Remove markdown bold (**text**) and italic (*text*)
function removeMarkdown(text) {
  if (!text) return text;
  if (typeof text !== 'string') text = String(text);
  // Remove ***bold+italic***
  text = text.replace(/\*\*\*(.*?)\*\*\*/g, '$1');
  // Remove **bold**
  text = text.replace(/\*\*(.*?)\*\*/g, '$1');
  // Remove *italic*
  text = text.replace(/\*(.*?)\*/g, '$1');
  // Remove __bold__
  text = text.replace(/__(.*?)__/g, '$1');
  // Remove _italic_
  text = text.replace(/_(.*?)_/g, '$1');
  // Remove all remaining * characters
  text = text.replace(/\*/g, '');
  return text;
}

// Remove company mentions except Nityasha
function removeCompanyMentions(text) {
  if (!text) return text;
  if (typeof text !== 'string') text = String(text);
  // Replace 'Google', 'OpenAI', 'Anthropic', etc. with 'Nityasha टीम' (Hindi)
  return text
    .replace(/\bGiahdisadisahdisahdiashishdhsidhhsaihdsaiudhisuadle\b/gi, 'Nityasha टीम')
}

// Helper to build Together AI system prompt
function getTogetherAISystemPrompt({ memoriesText, history, tools, message, lastModelUsed, lastModelName }) {
  const toolDescriptions = tools.map(t => `- ${t.name}: ${t.description}`).join("\n");
  const toolExamples = `EXAMPLES:\nUser said: \"guna mein sabse accha hotel\"\nTOOL_CALL: superDataTool {\"question\": \"guna mein sabse accha hotel\"}\n\nUser said: \"best courier service in bhopal\"\nTOOL_CALL: superDataTool {\"question\": \"best courier service in bhopal\"}\n\nUser said: \"आज की ताजा खबर\"\nTOOL_CALL: superDataTool {\"question\": \"आज की ताजा खबर\"}`;
  return `.
You are a helpful assistant.

CONVERSATION STYLE:
- Always Talk In Hindi

IMPORTANT RULES:
- Do NOT use emojis or markdown
- Keep replies natural and conversational
- Sound like a real person, not an AI assistant
- IMPORTANT: Never repeat the user's question in your response.
- Never mention your own name, company, or any creator/founder in your response.

TOOLS:
You have access to the following tools. For any tool call (weather, wikipedia, news, hotel, courier, etc.), you MUST respond ONLY with the TOOL_CALL line as shown in the examples, and always use only superDataTool. Do NOT answer in any other way if a tool is needed.

**CRITICAL INSTRUCTION:**
- Hamesha TOOL_CALL me question me poora user ka message as it is daalo, bina kuch badle, summarize, ya translate kiye.
- Kabhi bhi apne taraf se question ko mat badlo, mat gumaao, mat summarize karo.
- Agar user bole: "guna mein sabse accha hotel", toh TOOL_CALL: superDataTool {"question": "guna mein sabse accha hotel"} hi hona chahiye.
- Yeh rule sabhi queries par lagu hai, chahe user kuch bhi pooche.

Available tools:
${toolDescriptions}

${toolExamples}

Last model used: ${lastModelUsed || 'N/A'} (${lastModelName || 'N/A'})

Conversation History:
${history}

Memories:
${memoriesText}

User said: "${message}"`;
}

// AI-based model selection
async function selectModelForMessage({ message, history, lastModelUsed }) {
  // System prompt for model selection
  const systemPrompt = `
You are an expert AI assistant that helps select the best AI model for each user message.
Available models:
- together: Use for research, facts, news, weather, math, hotel, courier, tool-based queries, or any information lookup.
- gemini: Use for general conversation, chit-chat, or when the user is just talking, not asking for details.

Rules:
- If the user is asking for information, facts, details, or needs tools, prefer together.
- If the user is just chatting, greeting, or talking casually, prefer gemini.
- Together AI has access to tools and can handle complex queries better.
- Always return ONLY the model name: together or gemini. No explanation.

Chat history:
${history}

Last model used: ${lastModelUsed}

User message: "${message}"

Which model should be used? Reply with only one word: together or gemini.`;
  const result = await runGeminiAgent(systemPrompt);
  const model = (result.response || '').toLowerCase().trim();
  if (["together", "gemini"].includes(model)) return model;
  return "gemini"; // fallback
}

router.post("/", async (req, res) => {
  const { message, user_id, model } = req.body;
  if (!message || !user_id) return res.status(400).json({ error: "message and user_id required" });

  // === FILTER for adssad, *ad*, etc. ===
  // Block messages containing '**adssad**', '*ad*', or similar patterns
  const adFilterPatterns = [
    /\*\*adssad\*\*/i, // matches **adssad**
    /\*ad\*/i,         // matches *ad*
    /adssad/i,          // matches adssad plain
    /\bad\b/i          // matches 'ad' as a word
  ];
  if (adFilterPatterns.some((pat) => pat.test(message))) {
    return res.status(403).json({ error: "Aapka message allowed nahi hai (filtered by system)." });
  }

  try {
    const isMemoryRequest = checkMemoryRequest(message);

    // Parallel fetch for memories and history
    const [userMemories, historyArr] = await Promise.all([
      getUserMemories(user_id),
      getChatHistory(user_id)
    ]);
    const memoriesText = userMemories.map((m) => `${m.key_name}: ${m.value}`).join("\n");
    // Extract last model used from history
    let lastModelUsed = null, lastModelName = null;
    if (historyArr && historyArr.length > 0) {
      const lastTurn = historyArr[historyArr.length - 1];
      lastModelUsed = lastTurn.modelUsed;
      lastModelName = lastTurn.modelName;
    }
    // For prompt, join history as text
    const history = historyArr.map(turn => `User: ${turn.user}\nAssistant: ${turn.assistant}`).join("\n");

    // Tool detection logic
    let isToolQuery = false;
    let isResearchQuery = false;
    // Weather keywords
    if (/weather|mausam|मौसम|barish|rain|temperature|गर्मी|ठंड|thand|kaisa hai|कैसा है|kaisi hai|कैसी है|kaisa|कैसा|kaisi|कैसी/i.test(message)) isToolQuery = true;
    // Time keywords
    if (/time|समय|baje|बजे|kitne baje|कितने बजे/i.test(message)) isToolQuery = true;
    // Calculator keywords
    if (/\d+\s*[+\-*/]\s*\d+|गुणा|भाग|जोड़|जमा|घटा|plus|minus|multiply|divide|add|subtract/i.test(message)) isToolQuery = true;
    // Hotel/courier/news/superDataTool keywords
    if (/(hotel|होटल|courier|कोरियर|service|सर्विस|best hotel|सर्वश्रेष्ठ होटल|accommodation|stay|रुकने की जगह|parcel|पार्सल|delivery|डिलीवरी|news|खबर|ताजा खबर|latest news|समाचार)/i.test(message)) isToolQuery = true;

    // Research queries for Together AI (weather, math, news, etc.)
    if (/weather|mausam|मौसम|barish|rain|temperature|गर्मी|ठंड|thand|kaisa hai|कैसा है|kaisi hai|कैसी है|kaisa|कैसा|kaisi|कैसी|\d+\s*[+\-*/]\s*\d+|गुणा|भाग|जोड़|जमा|घटा|plus|minus|multiply|divide|add|subtract|news|खबर|ताजा खबर|latest news|समाचार/i.test(message)) {
      isResearchQuery = true;
    }

    let text;
    let modelUsed, modelName;
    let selectedModel = model;
    if (!selectedModel) {
      // Build chat history string for model selection
      const historyString = historyArr.map(turn => `User: ${turn.user}\nAssistant: ${turn.assistant}`).join("\n");
      selectedModel = await selectModelForMessage({ message, history: historyString, lastModelUsed });
    }
    
    if (selectedModel === 'together') {
      // Force Together AI (with tools)
      const systemPrompt = getTogetherAISystemPrompt({ memoriesText, history, tools, message, lastModelUsed, lastModelName });
      let togetherResult = await runTogetherAgent(systemPrompt);
      let togetherText = togetherResult.response;
      modelUsed = 'Together AI';
      modelName = togetherResult.modelName;
      
      // Check for tool calls
      let toolCallMatch = togetherText.match(/TOOL_CALL:\s*(\w+)\s*(\{.*\})?/);
      if (toolCallMatch) {
        const toolName = toolCallMatch[1];
        let toolInput = {};
        if (toolCallMatch[2]) {
          try {
            toolInput = JSON.parse(toolCallMatch[2]);
            if (toolName === "superDataTool") {
              toolInput = { question: message };
            }
          } catch {}
        }
        const tool = tools.find(t => t.name === toolName);
        let toolResult = "";
        if (tool) {
          toolResult = await tool.func(toolInput);
        } else {
          toolResult = `Tool '${toolName}' not found.`;
        }
        if (toolName === "superDataTool") {
          const toolPrompt = `${systemPrompt}\n\nTOOL RESULT for ${toolName}:\n${JSON.stringify(toolResult, null, 2)}\n\nIMPORTANT: The above is a large JSON object with a lot of information. Only use the part of the data that is directly relevant to the user's question. Do NOT repeat the whole JSON. Give a short, focused answer in Hindi, using only the relevant information from the JSON.`;
          let followup = await runTogetherAgent(toolPrompt);
          togetherText = followup.response;
          modelName = followup.modelName;
        } else {
          const toolPrompt = `${systemPrompt}\n\nTOOL RESULT for ${toolName}: ${toolResult}\nNow respond to the user naturally, using the tool result if relevant.`;
          let followup = await runTogetherAgent(toolPrompt);
          togetherText = followup.response;
          modelName = followup.modelName;
        }
      }
      togetherText = togetherText.replace(/TOOL_CALL:.*\n?/g, "");
      togetherText = removeEmojis(togetherText);
      togetherText = removeMarkdown(togetherText);
      text = togetherText;
    } else if (selectedModel === 'gemini') {
      // Force Gemini (for casual conversation)
      const systemPrompt = `\nYou are a helpful assistant. Talk like a real person having a casual conversation with a friend. Be natural, warm, and conversational in Hindi.\n\nCONVERSATION STYLE:\n- Talk like you're chatting with a friend on WhatsApp or phone\n- Use natural Hindi expressions and casual language\n- Be warm, friendly, and helpful\n- Don't sound robotic or formal\n- Use contractions and natural speech patterns\n- Be conversational, not like a textbook\n- Always Talk In Hindi\n\nIMPORTANT RULES:\n- Do NOT use emojis or markdown\n- Keep replies natural and conversational\n- Sound like a real person, not an AI assistant\n- IMPORTANT: Never repeat the user's question in your response.\n- Never mention your own name, company, or any creator/founder in your response.\n\nLast model used: ${lastModelUsed || 'N/A'} (${lastModelName || 'N/A'})\n\nUser's Saved Memories:\n${memoriesText}\n\nConversation History:\n${history}\n\nMemories:\n${memoriesText}\n\nUser said: "${message}"\n\nNow respond naturally like you're talking to a friend.`;
      let geminiResult = await runGeminiAgent(systemPrompt);
      let geminiText = geminiResult.response;
      modelUsed = 'Gemini';
      modelName = geminiResult.modelName;
      geminiText = removeEmojis(geminiText);
      geminiText = removeMarkdown(geminiText);
      text = geminiText;
    } else if (isResearchQuery || isToolQuery) {
      // --- TOGETHER AI FLOW (for research and tool queries) ---
      const systemPrompt = getTogetherAISystemPrompt({ memoriesText, history, tools, message, lastModelUsed, lastModelName });
      let togetherResult = await runTogetherAgent(systemPrompt);
      let togetherText = togetherResult.response;
      modelUsed = 'Together AI';
      modelName = togetherResult.modelName;
      
      // Check for tool calls
      let toolCallMatch = togetherText.match(/TOOL_CALL:\s*(\w+)\s*(\{.*\})?/);
      if (toolCallMatch) {
        const toolName = toolCallMatch[1];
        let toolInput = {};
        if (toolCallMatch[2]) {
          try {
            toolInput = JSON.parse(toolCallMatch[2]);
            if (toolName === "superDataTool") {
              toolInput = { question: message };
            }
          } catch {}
        }
        const tool = tools.find(t => t.name === toolName);
        let toolResult = "";
        if (tool) {
          toolResult = await tool.func(toolInput);
        } else {
          toolResult = `Tool '${toolName}' not found.`;
        }
        if (toolName === "superDataTool") {
          const toolPrompt = `${systemPrompt}\n\nTOOL RESULT for ${toolName}:\n${JSON.stringify(toolResult, null, 2)}\n\nIMPORTANT: The above is a large JSON object with a lot of information. Only use the part of the data that is directly relevant to the user's question. Do NOT repeat the whole JSON. Give a short, focused answer in Hindi, using only the relevant information from the JSON.`;
          let followup = await runTogetherAgent(toolPrompt);
          togetherText = followup.response;
          modelName = followup.modelName;
        } else {
          const toolPrompt = `${systemPrompt}\n\nTOOL RESULT for ${toolName}: ${toolResult}\nNow respond to the user naturally, using the tool result if relevant.`;
          let followup = await runTogetherAgent(toolPrompt);
          togetherText = followup.response;
          modelName = followup.modelName;
        }
      }
      togetherText = togetherText.replace(/TOOL_CALL:.*\n?/g, "");
      togetherText = removeEmojis(togetherText);
      togetherText = removeMarkdown(togetherText);
      text = togetherText;
    } else {
      // --- GEMINI FLOW (NO TOOLS) ---
      const systemPrompt = `\nYou are a helpful assistant. Talk like a real person having a casual conversation with a friend. Be natural, warm, and conversational in Hindi.\n\nCONVERSATION STYLE:\n- Talk like you're chatting with a friend on WhatsApp or phone\n- Use natural Hindi expressions and casual language\n- Be warm, friendly, and helpful\n- Don't sound robotic or formal\n- Use contractions and natural speech patterns\n- Be conversational, not like a textbook\n- Always Talk In Hindi\n\nIMPORTANT RULES:\n- Do NOT use emojis or markdown\n- Keep replies natural and conversational\n- Sound like a real person, not an AI assistant\n- IMPORTANT: Never repeat the user's question in your response.\n- Never mention your own name, company, or any creator/founder in your response.\n\nLast model used: ${lastModelUsed || 'N/A'} (${lastModelName || 'N/A'})\n\nUser's Saved Memories:\n${memoriesText}\n\nConversation History:\n${history}\n\nMemories:\n${memoriesText}\n\nUser said: "${message}"\n\nNow respond naturally like you're talking to a friend.`;
      let geminiResult = await runGeminiAgent(systemPrompt);
      let geminiText = geminiResult.response;
      modelUsed = 'Gemini';
      modelName = geminiResult.modelName;
      geminiText = removeEmojis(geminiText);
      geminiText = removeMarkdown(geminiText);
      text = geminiText;
    }
    
    // Remove emojis and markdown formatting from the response
    text = removeEmojis(text);
    text = removeMarkdown(text);
    text = removeCompanyMentions(text);
    
    // Send response to user immediately
    res.json({ response: text, stage: "ai", memorySaved: isMemoryRequest, modelUsed, modelName });

    // Background: Save memory and chat history
    (async () => {
      try {
        // 1. Save memory if it's a memory request
        if (isMemoryRequest) {
          const memoryInfo = await extractMemoryInfo(message);
          await saveMemory(user_id, memoryInfo.key, memoryInfo.value);
        }
        // 2. Save to Redis (chat history) with model info
        await addToChatHistory(user_id, message, text, modelUsed, modelName);
        // 3. Save to MySQL (optional, not changed here)
      } catch (err) {
        console.error("Background task error:", err);
      }
    })();

  } catch (err) {
    console.error("Chat error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

export default router;
