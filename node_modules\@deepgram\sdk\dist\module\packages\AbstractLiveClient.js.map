{"version": 3, "file": "AbstractLiveClient.js", "sourceRoot": "", "sources": ["../../../src/packages/AbstractLiveClient.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,OAAO,EAAE,cAAc,EAAE,IAAI,EAAE,MAAM,kBAAkB,CAAC;AACxD,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAGnE,OAAO,EAAE,KAAK,EAAE,MAAM,gBAAgB,CAAC;AACvC,OAAO,EAAE,sBAAsB,EAAE,MAAM,eAAe,CAAC;AA+BvD;;;;;GAKG;AACH,iCAAiC;AACjC,gBAAgB;AAChB,qBAAqB;AACrB,kBAAkB;AAClB,IAAI;AAEJ;;GAEG;AACH,MAAM,0BAA0B,GAAG,OAAO,SAAS,KAAK,WAAW,CAAC;AAEpE;;;;;;GAMG;AACH,MAAM,OAAgB,kBAAmB,SAAQ,cAAc;IAM7D,YAAY,OAA8B;QACxC,KAAK,CAAC,OAAO,CAAC,CAAC;QAJV,SAAI,GAAyB,IAAI,CAAC;QAClC,eAAU,GAAe,EAAE,CAAC;QAwHnC;;;;WAIG;QACI,cAAS,GAAkC,IAAI,CAAC;QAxHrD,MAAM,EACJ,GAAG,EACH,SAAS,EAAE,EAAE,OAAO,EAAE,gBAAgB,EAAE,MAAM,EAAE,GACjD,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAE1B,IAAI,IAAI,CAAC,KAAK,EAAE;YACd,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,KAAM,CAAC,GAAG,CAAC;SAC5C;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,GAAG,CAAC;SACrC;QAED,IAAI,MAAM,EAAE;YACV,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC;SACzB;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;SACvB;QAED,IAAI,gBAAgB,CAAC,gBAAgB,EAAE;YACrC,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;SAClD;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;SACnB;QAED,IAAI,CAAC,CAAC,eAAe,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACtC,IAAI,IAAI,CAAC,WAAW,EAAE;gBACpB,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,UAAU,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,yBAAyB;aACxF;iBAAM;gBACL,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,EAAE,CAAC,CAAC,oBAAoB;aACrE;SACF;IACH,CAAC;IAED;;;;OAIG;IACO,OAAO,CAAC,oBAAgC,EAAE,QAAgB;QAClE,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,OAAO;SACR;QAED,IAAI,CAAC,SAAS,GAAG,CAAC,OAAO,GAAG,oBAAoB,EAAE,EAAE;YAClD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAClC,CAAC,CAAC;QAEF,MAAM,UAAU,GAAG,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,EAAE,EAAE,oBAAoB,CAAC,CAAC;QAC1E,MAAM,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;QACrC,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC;QAExB,IAAI,CAAC,WAAW,IAAI,CAAC,MAAM,EAAE;YAC3B,MAAM,IAAI,KAAK,CAAC,2DAA2D,CAAC,CAAC;SAC9E;QAED;;WAEG;QACH,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI,CAAC,SAAS,CAAC,UAAU,EAAE,SAAS,EAAE;gBACpD,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO;SACR;QAED;;;;;WAKG;QACH,IAAI,KAAK,EAAE,EAAE;YACX,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;gBACpC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,UAAU,EAAE;oBAC7B,OAAO,EAAE,IAAI,CAAC,OAAO;iBACtB,CAAC,CAAC;gBACH,OAAO,CAAC,GAAG,CAAC,kBAAkB,CAAC,CAAC;YAClC,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO;SACR;QAED;;WAEG;QACH,IAAI,0BAA0B,EAAE;YAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,SAAS,CACvB,UAAU,EACV,WAAW,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,EAAE,MAAO,CAAC,CAC3D,CAAC;YACF,IAAI,CAAC,eAAe,EAAE,CAAC;YACvB,OAAO;SACR;QAED;;WAEG;QACH,IAAI,CAAC,IAAI,GAAG,IAAI,gBAAgB,CAAC,UAAU,EAAE,SAAS,EAAE;YACtD,KAAK,EAAE,GAAG,EAAE;gBACV,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;YACnB,CAAC;SACF,CAAC,CAAC;QAEH;;WAEG;QACH,MAAM,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE,EAAE,EAAE,EAAE;YACpC,IAAI,CAAC,IAAI,GAAG,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE;gBACxC,OAAO,EAAE,IAAI,CAAC,OAAO;aACtB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,EAAE,CAAC;IACzB,CAAC;IASD;;;;;OAKG;IACI,UAAU,CAAC,IAAa,EAAE,MAAe;QAC9C,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,cAAa,CAAC,CAAC,CAAC,OAAO;YAC3C,IAAI,IAAI,EAAE;gBACR,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,CAAC,CAAC;aACrC;iBAAM;gBACL,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;SAClB;IACH,CAAC;IAED;;;;OAIG;IACI,eAAe;QACpB,QAAQ,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACzC,KAAK,aAAa,CAAC,UAAU;gBAC3B,OAAO,gBAAgB,CAAC,UAAU,CAAC;YACrC,KAAK,aAAa,CAAC,IAAI;gBACrB,OAAO,gBAAgB,CAAC,IAAI,CAAC;YAC/B,KAAK,aAAa,CAAC,OAAO;gBACxB,OAAO,gBAAgB,CAAC,OAAO,CAAC;YAClC;gBACE,OAAO,gBAAgB,CAAC,MAAM,CAAC;SAClC;IACH,CAAC;IAED;;;;OAIG;IACI,aAAa;;QAClB,OAAO,MAAA,MAAA,IAAI,CAAC,IAAI,0CAAE,UAAU,mCAAI,aAAa,CAAC,MAAM,CAAC;IACvD,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,OAAO,IAAI,CAAC,eAAe,EAAE,KAAK,gBAAgB,CAAC,IAAI,CAAC;IAC1D,CAAC;IAED;;;;;;OAMG;IACH,IAAI,CAAC,IAAoB;QACvB,MAAM,QAAQ,GAAG,GAAS,EAAE;;YAC1B,IAAI,IAAI,YAAY,IAAI,EAAE;gBACxB,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,EAAE;oBACnB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,oCAAoC,EAAE,IAAI,CAAC,CAAC;oBAE7D,OAAO;iBACR;gBAED,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;aACjC;YAED,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;gBAC5B,IAAI,CAAC,CAAA,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,UAAU,CAAA,EAAE;oBACrB,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,uCAAuC,EAAE,IAAI,CAAC,CAAC;oBAEhE,OAAO;iBACR;aACF;YAED,MAAA,IAAI,CAAC,IAAI,0CAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACxB,CAAC,CAAA,CAAC;QAEF,IAAI,IAAI,CAAC,WAAW,EAAE,EAAE;YACtB,QAAQ,EAAE,CAAC;SACZ;aAAM;YACL,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChC;IACH,CAAC;IAED;;;OAGG;IACH,IAAI,KAAK;;QACP,OAAO,IAAI,CAAC,GAAG,KAAK,OAAO,IAAI,CAAC,CAAC,CAAA,MAAA,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,0CAAE,GAAG,CAAA,CAAC;IACtF,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA8CG;IACO,uBAAuB,CAC/B,KAAyB,EACzB,IAAoB;;QAQpB,MAAM,SAAS,GAMX,EAAE,CAAC;QAEP,uCAAuC;QACvC,IAAI,IAAI,EAAE;YACR,SAAS,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACvC,SAAS,CAAC,GAAG,GAAG,OAAO,IAAI,CAAC,GAAG,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAA,IAAI,CAAC,GAAG,0CAAE,QAAQ,EAAE,CAAC;SAChF;QAED,sEAAsE;QACtE,iFAAiF;QACjF,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,EAAE;YACpC,MAAM,MAAM,GAAG,IAAW,CAAC;YAE3B,uDAAuD;YACvD,IAAI,MAAM,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClC,SAAS,CAAC,UAAU,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;gBAElD,wCAAwC;gBACxC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE;oBAC3B,SAAS,CAAC,eAAe,qBAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAE,CAAC;oBAE3D,oDAAoD;oBACpD,MAAM,SAAS,GACb,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,eAAe,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,iBAAiB,CAAC,CAAC;oBACzF,IAAI,SAAS,EAAE;wBACb,SAAS,CAAC,SAAS,GAAG,SAAS,CAAC;qBACjC;iBACF;aACF;YAED,kEAAkE;YAClE,IAAI,KAAK,IAAI,QAAQ,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;gBAC9C,MAAM,MAAM,GAAG,KAAK,CAAC,MAAa,CAAC;gBACnC,IAAI,MAAM,CAAC,GAAG,EAAE;oBACd,SAAS,CAAC,GAAG,GAAG,MAAM,CAAC,GAAG,CAAC;iBAC5B;gBACD,IAAI,MAAM,CAAC,UAAU,KAAK,SAAS,EAAE;oBACnC,SAAS,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,CAAC;iBAC1C;aACF;SACF;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAED;;;;;;;;OAQG;IACO,mBAAmB,CAC3B,KAAyB,EACzB,YAMC;QAED,mDAAmD;QACnD,MAAM,aAAa,GAAG,IAAI,sBAAsB,CAC7C,KAAoB,CAAC,OAAO,IAAI,4BAA4B,kBAE3D,aAAa,EAAE,KAAK,IACjB,YAAY,EAElB,CAAC;QAEF,yDAAyD;QACzD,uCAAuC;QACvC,uCAEK,KAAK;YACR,6BAA6B;YAC7B,KAAK,EAAE,aAAa;YACpB,sCAAsC;YACtC,UAAU,EAAE,YAAY,CAAC,UAAU,EACnC,SAAS,EAAE,YAAY,CAAC,SAAS,EACjC,eAAe,EAAE,YAAY,CAAC,eAAe,EAC7C,GAAG,EAAE,YAAY,CAAC,GAAG,EACrB,UAAU,EAAE,YAAY,CAAC,UAAU;YACnC,qCAAqC;YACrC,OAAO,EAAE,IAAI,CAAC,yBAAyB,CAAC,KAAK,EAAE,YAAY,CAAC,IAC5D;IACJ,CAAC;IAED;;;;;;OAMG;IACO,yBAAyB,CACjC,KAAyB,EACzB,YAMC;QAED,IAAI,OAAO,GAAI,KAAoB,CAAC,OAAO,IAAI,4BAA4B,CAAC;QAE5E,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,YAAY,CAAC,UAAU,EAAE;YAC3B,OAAO,CAAC,IAAI,CAAC,WAAW,YAAY,CAAC,UAAU,EAAE,CAAC,CAAC;SACpD;QAED,IAAI,YAAY,CAAC,SAAS,EAAE;YAC1B,OAAO,CAAC,IAAI,CAAC,eAAe,YAAY,CAAC,SAAS,EAAE,CAAC,CAAC;SACvD;QAED,IAAI,YAAY,CAAC,UAAU,KAAK,SAAS,EAAE;YACzC,MAAM,UAAU,GAAG,CAAC,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC/D,MAAM,SAAS,GACb,UAAU,CAAC,YAAY,CAAC,UAAU,CAAC,IAAI,WAAW,YAAY,CAAC,UAAU,GAAG,CAAC;YAC/E,OAAO,CAAC,IAAI,CAAC,gBAAgB,SAAS,EAAE,CAAC,CAAC;SAC3C;QAED,IAAI,YAAY,CAAC,GAAG,EAAE;YACpB,OAAO,CAAC,IAAI,CAAC,QAAQ,YAAY,CAAC,GAAG,EAAE,CAAC,CAAC;SAC1C;QAED,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;YACtB,OAAO,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;SACvC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED;;;;;;;;;OASG;IACO,qBAAqB,CAAC,MAAsD;QACpF,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,EAAE;gBACtB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;YAC/B,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAU,EAAE,EAAE;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;YACjC,CAAC,CAAC;YAEF,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAiB,EAAE,EAAE;gBACxC,MAAM,YAAY,GAAG,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,IAAI,CAAC,IAAI,IAAI,SAAS,CAAC,CAAC;gBACjF,MAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;gBACpE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,aAAa,CAAC,CAAC;YACzC,CAAC,CAAC;SACH;IACH,CAAC;CAQF;AAED,MAAM,gBAAgB;IAWpB,YAAY,OAAY,EAAE,UAAqB,EAAE,OAA4B;QAV7E,eAAU,GAAW,aAAa,CAAC;QAEnC,YAAO,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC7B,YAAO,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC7B,cAAS,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC/B,WAAM,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC5B,eAAU,GAAW,aAAa,CAAC,UAAU,CAAC;QAC9C,SAAI,GAAa,GAAG,EAAE,GAAE,CAAC,CAAC;QAC1B,QAAG,GAAwB,IAAI,CAAC;QAG9B,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,QAAQ,EAAE,CAAC;QAC9B,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC7B,CAAC;CACF;AAED,OAAO,EAAE,kBAAkB,IAAI,gBAAgB,EAAE,CAAC"}