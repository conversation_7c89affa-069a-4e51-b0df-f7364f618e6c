import express from "express";
import {
  checkMemoryRequest,
  extractMemoryInfo,
  saveMemory,
  getUserMemories,
} from "../utils/memory.js";
import { getChatHistory, addToChatHistory } from "../utils/history.js";
import Redis from "redis";
import { runGeminiAgent } from "../utils/aiAgent.js";
import { tools } from "../utils/tools.js";
import { createClient } from "@deepgram/sdk"; // Deepgram SDK v3
import fs from "fs"; // optional, for debug

const router = express.Router();

const DEEPGRAM_API_KEY = "****************************************"; // Replace with env variable
const deepgram = createClient(DEEPGRAM_API_KEY);

// Redis client setup
const redis = Redis.createClient({
  socket: {
    host: "mercury.nityasha.com",
    port: 26739,
  },
  password: "Amber@!23",
});

redis.on("error", (err) => console.log("Redis Client Error:", err));
redis.connect().catch(console.error);

// Utility: Remove emojis
function removeEmojis(text) {
  return text.replace(
    /[😀-🙏]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
    ""
  );
}

// Utility: Clean up text
function removeSpecialChars(text) {
  return text.replace(/[^\u0900-\u097Fa-zA-Z0-9 .,!?\-@#'"\n]/g, "");
}

router.post("/voiceai", async (req, res) => {
  let { message, user_id, base64Audio } = req.body;
  if (!user_id) return res.status(400).json({ error: "user_id required" });

  try {
    // Transcribe audio if provided
    if (base64Audio && !message) {
      const audioBuffer = Buffer.from(base64Audio, "base64");

      // Optional — save file for debugging
      // fs.writeFileSync("/tmp/input.wav", audioBuffer);

      console.log("🔈 Audio Buffer Size:", audioBuffer.length);

      if (audioBuffer.length < 1000) {
        return res.status(400).json({ error: "Invalid or empty audio buffer." });
      }

      const { result, error } = await deepgram.listen.prerecorded.transcribeFile(
        audioBuffer,
        {
          language: "hi",
          punctuate: true,
          smart_format: true,
          model: "general", // or nova-2 depending on your Deepgram plan
        }
      );

      if (error) {
        console.error("🛑 Deepgram Error:", error);
        return res.status(500).json({ error: "Transcription error", detail: error });
      }

      console.log("✅ Deepgram Result:", JSON.stringify(result, null, 2));

      message =
        result?.results?.channels?.[0]?.alternatives?.[0]?.transcript?.trim();

      if (!message) {
        return res.status(400).json({ error: "Transcription failed." });
      }
    }

    if (!message) return res.status(400).json({ error: "message required" });

    const isMemoryRequest = checkMemoryRequest(message);

    const [userMemories, history] = await Promise.all([
      getUserMemories(user_id),
      getChatHistory(user_id),
    ]);

    const memoriesText = userMemories.map(m => `${m.key_name}: ${m.value}`).join("\n");
    const toolDescriptions = tools.map(t => `- ${t.name}: ${t.description}`).join("\n");
    const toolExamples = `EXAMPLES:
User said: "What time is it in India?"
TOOL_CALL: getCurrentTimeIndia {}
...`;

    const systemPrompt = `
आप नित्याशा हैं, एक मिलनसार और मददगार सहायक। किसी दोस्त से सहज बातचीत करते हुए एक वास्तविक व्यक्ति की तरह बात करें। सहज, गर्मजोशी से और हिंदी में बातचीत करें।

CONVERSATION STYLE:
- ऐसे बात करें जैसे आप किसी दोस्त से व्हाट्सएप या फोन पर बात कर रहे हों
- स्वाभाविक हिंदी भावों और सहज भाषा का प्रयोग करें
- गर्मजोशी, मित्रता और मददगार बनें
- रोबोट जैसा या औपचारिक न लगें
- संकुचन और स्वाभाविक भाषण शैली का प्रयोग करें
- बातचीत जैसा व्यवहार करें, पाठ्यपुस्तक जैसा नहीं

IMPORTANT RULES:
- इमोजी या मार्कडाउन का इस्तेमाल न करें
- जवाब स्वाभाविक और बातचीत वाले रखें
- किसी वास्तविक व्यक्ति की तरह बोलें, AI सहायक की तरह नहीं

TOOLS:
You have access to the following tools. If the user asks for weather, time, a calculation, Wikipedia, or web search, you MUST respond ONLY with the TOOL_CALL line as shown in the examples. Do NOT answer in any other way if a tool is needed.

Available tools:
${toolDescriptions}

${toolExamples}

User's Saved Memories:
${memoriesText}

Conversation History:
${history}

User said: "${message}"

अब स्वाभाविक रूप से ऐसे जवाब दें जैसे आप किसी मित्र से बात कर रहे हों, या यदि आवश्यक हो तो ऊपर बताए अनुसार किसी उपकरण को बुलाएँ:`;

    let toolsUsed = [];
    let text = await runGeminiAgent(systemPrompt);

    const toolCallMatch = text.match(/TOOL_CALL:\s*(\w+)\s*(\{.*\})?/);
    if (toolCallMatch) {
      const toolName = toolCallMatch[1];
      let toolInput = {};

      if (toolCallMatch[2]) {
        try {
          toolInput = JSON.parse(toolCallMatch[2]);
        } catch {
          console.warn("⚠️ Tool input parse error");
        }
      }

      const tool = tools.find(t => t.name === toolName);
      let toolResult = "";

      if (tool) {
        toolResult = await tool.func(toolInput);
      } else {
        toolResult = `Tool '${toolName}' not found.`;
      }

      toolsUsed.push({ name: toolName, result: toolResult });

      const toolPrompt = `${systemPrompt}\n\nTOOL RESULT for ${toolName}: ${toolResult}\nNow respond to the user naturally, using the tool result if relevant.`;
      text = await runGeminiAgent(toolPrompt);
    } else {
      // Fallback mechanisms
      let fallbackTool = null;
      let fallbackInput = {};

      const weatherMatch = message.match(/weather|mausam|मौसम|barish|rain|temperature|गर्मी|ठंड|thand|kaisa hai|कैसा है|कैसी है|kaisi|कैसी/i);
      if (weatherMatch) {
        fallbackTool = tools.find(t => t.name === "weather");
        const cityMatch = message.match(/in ([a-zA-Z]+)|ka ([a-zA-Z]+)|का ([\p{L}]+)/u);
        let city = "India";
        if (cityMatch) city = cityMatch[1] || cityMatch[2] || cityMatch[3];
        fallbackInput = { city };
      }

      if (!fallbackTool && /time|समय|baje|बजे|kitne baje|कितने बजे/i.test(message)) {
        fallbackTool = tools.find(t => t.name === "getCurrentTimeIndia");
      }

      if (!fallbackTool && /\d+\s*[+\-*/×÷]\s*\d+|गुणा|भाग|जोड़|घटा|multiply|divide|plus|minus|add|subtract/i.test(message)) {
        fallbackTool = tools.find(t => t.name === "calculator");
        fallbackInput = { expression: message };
      }

      if (fallbackTool) {
        const toolResult = await fallbackTool.func(fallbackInput);
        toolsUsed.push({ name: fallbackTool.name, result: toolResult });

        const toolPrompt = `${systemPrompt}\n\nTOOL RESULT for ${fallbackTool.name}: ${toolResult}\nNow respond to the user naturally, using the tool result if relevant.`;
        text = await runGeminiAgent(toolPrompt);
      }
    }

    // Cleanup tool instructions
    text = text.replace(/TOOL_CALL:.*\n?/g, "");
    const cleanMessage = removeSpecialChars(message);

    res.json({
      response: text,
      stage: "ai",
      memorySaved: isMemoryRequest,
      toolsUsed,
    });

    (async () => {
      if (isMemoryRequest) {
        const memoryInfo = await extractMemoryInfo(message);
        await saveMemory(user_id, memoryInfo.key, memoryInfo.value);
      }
      await addToChatHistory(user_id, message, text, toolsUsed);
    })();

  } catch (err) {
    console.error("🔥 Chat error:", err);
    res.status(500).json({ error: "Internal server error" });
  }
});

export default router;
