// utils/db.js
import mysql from 'mysql2/promise';

const pool = mysql.createPool({
      host: process.env.MYSQL_HOST || 'mercury.nityasha.com',
  user: process.env.MYSQL_USER || 'kzzuezbs_31aa9913123139jmasr',
  password: process.env.MYSQL_PASSWORD || 'N4(I9_P9>!lPo:vmT0',
  database: process.env.MYSQL_DATABASE || 'kzzuezbs_31aa9913123139jmasr',
   waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// Default export (recommended way)
export default pool;

// Optional named export
export function getMysqlConnection() {
  return pool;
}
