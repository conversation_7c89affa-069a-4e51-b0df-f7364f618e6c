import { GoogleGenerativeA<PERSON> } from "@google/generative-ai";
import axios from "axios";

// ✅ Your Gemini API keys
const GEMINI_KEYS = [
  "AIzaSyCl6gI7KYssoJK7P_osPd7W7QAizNHrBlQ",
  "AIzaSyDZWlX_-t-QPf0YmXog9Janu3FWnPbHtyk",
  "AIzaSyDwfucmKX1DRvWRG_vVDpXDJxioxUf0j-g"
];

// ✅ Gemini models in fallback order
const GEMINI_MODELS = [
  "gemini-2.5-flash-lite-preview-06-17",
  "gemini-1.5-pro",
  "gemini-1.5-flash"
];

// ✅ Your Together API keys
const TOGETHER_KEYS = [
  "728122776d61611edc84ceb621686b3bb9258ed391a84f9d13d0412ab0874e1b",
  "8d39fedf0f485c168b1f5680a9674ccb1407b19d5012b08c64a8f951a9d2d472",
];

// ✅ Together model
const TOGETHER_MODELS = [
  "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
  "meta-llama/Llama-Vision-Free",
  "lgai/exaone-3-5-32b-instruct",
  "lgai/exaone-deep-32b",
  "arcee-ai/AFM-4.5B-Preview",
  "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
];

// ✅ OpenRouter setup
const OPENROUTER_KEY = "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";
const OPENROUTER_MODEL = [
      "openai/gpt-4.1-nano",
  "openai/gpt-4o-mini",
  "openai/gpt-4o",
  "anthropic/claude-3.5-sonnet",
  "anthropic/claude-3-haiku",
  "meta-llama/llama-3.1-8b-instruct",
  "meta-llama/llama-3.1-70b-instruct",
  "google/gemini-pro",
  "mistralai/mistral-7b-instruct",
  "microsoft/wizardlm-2-8x22b",
  "google/gemini-2.0-flash-exp:free",
  "mistralai/mistral-small-24b-instruct-2501:free",
  "qwen/qwen3-30b-a3b:free",
  "sarvamai/sarvam-m:free",
  "qwen/qwen3-30b-a3b:free"
  ] // or any model you prefer


// 👇 All fallback logic in one single function
export async function runGeminiAgent(userMessage) {
  // 1️⃣ Gemini
  for (const key of GEMINI_KEYS) {
    const genAI = new GoogleGenerativeAI(key);
    for (const modelName of GEMINI_MODELS) {
      try {
        const model = genAI.getGenerativeModel({ model: modelName });
        const result = await model.generateContent(userMessage);
        const reply = result?.response?.text();
        if (reply) {
          console.log(`✅ Gemini: key=${key}, model=${modelName}`);
          return { response: reply, modelName };
        }
      } catch (err) {
        console.warn(`⚠️ Gemini failed (key=${key}, model=${modelName}):`, err.message);
      }
    }
  }

  // 2️⃣ Together AI
  for (const togetherKey of TOGETHER_KEYS) {
    for (const togetherModel of TOGETHER_MODELS) {
      try {
        const response = await axios.post(
          "https://api.together.xyz/v1/chat/completions",
          {
            model: togetherModel,
            messages: [{ role: "user", content: userMessage }],
            temperature: 0.7,
          },
          {
            headers: {
              Authorization: `Bearer ${togetherKey}`,
              "Content-Type": "application/json",
            },
          }
        );

        const content = response?.data?.choices?.[0]?.message?.content;
        if (content) {
          console.log(`✅ Together AI: key=${togetherKey}, model=${togetherModel}`);
          return { response: content, modelName: togetherModel };
        }
      } catch (err) {
        console.warn(`⚠️ Together failed (key=${togetherKey}, model=${togetherModel}):`, err.message);
      }
    }
  }

  // 3️⃣ OpenRouter
  try {
    const response = await axios.post(
      "https://openrouter.ai/api/v1/chat/completions",
      {
        model: OPENROUTER_MODEL,
        messages: [{ role: "user", content: userMessage }],
      },
      {
        headers: {
          Authorization: `Bearer ${OPENROUTER_KEY}`,
          "Content-Type": "application/json",
        },
      }
    );

    const content = response?.data?.choices?.[0]?.message?.content;
    if (content) {
      console.log(`✅ OpenRouter: model=${OPENROUTER_MODEL}`);
      return { response: content, modelName: OPENROUTER_MODEL };
    }
  } catch (err) {
    console.warn(`⚠️ OpenRouter failed:`, err.message);
  }

  // ❌ All failed
  return { response: "Sorry, all AI services are currently unavailable.", modelName: null };
}

// Exported Together AI only agent
export async function runTogetherAgent(userMessage) {
  for (const togetherKey of TOGETHER_KEYS) {
    for (const togetherModel of TOGETHER_MODELS) {
      try {
        const response = await axios.post(
          "https://api.together.xyz/v1/chat/completions",
          {
            model: togetherModel,
            messages: [{ role: "user", content: userMessage }],
            temperature: 0.7,
          },
          {
            headers: {
              Authorization: `Bearer ${togetherKey}`,
              "Content-Type": "application/json",
            },
          }
        );
        const content = response?.data?.choices?.[0]?.message?.content;
        if (content) {
          console.log(`✅ Together AI: key=${togetherKey}, model=${togetherModel}`);
          return { response: content, modelName: togetherModel };
        }
      } catch (err) {
        console.warn(`⚠️ Together failed (key=${togetherKey}, model=${togetherModel}):`, err.message);
      }
    }
  }
  return { response: "Sorry, all Together AI services are currently unavailable.", modelName: null };
}

// MetaAI agent for research queries (weather, math, news, etc.)
import fetch from 'node-fetch';

export async function runMetaAIAgent(systemPrompt, message) {
  const payload = {
    message: `${systemPrompt}\nUser: ${message}`
  };
  const response = await fetch('http://103.86.176.140:8785/prompt', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(payload)
  });
  if (!response.ok) throw new Error('MetaAI API error');
  const data = await response.json();
  return {
    response: data.response || data.result || '',
    modelName: 'MetaAI'
  };
}