import axios from "axios";

// Default model selector
const MODEL = process.env.AI_MODEL || "gemini";

// ========== Gemini Completion ==========
async function geminiComplete(prompt) {
  const keys = [process.env.GEMINI_API_KEY_PRIMARY, process.env.GEMINI_API_KEY_SECONDARY , process.env.GEMINI_API_KEY_FORTH];
  const body = {
    contents: [{ parts: [{ text: prompt }] }]
    
  };

  for (const key of keys) {
    try {
      const res = await axios.post(
        "https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent",
        body,
        {
          headers: {
            "Content-Type": "application/json",
            "x-goog-api-key": key
          }
        },
      );
      const text = res.data.candidates?.[0]?.content?.parts?.[0]?.text?.trim();
      if (text) return text;
    } catch (err) {
      console.warn("Gemini text failed:", err.message);
    }
  }
  return null;
}

// ========== XAI Completion ==========
async function xaiComplete(prompt) {
  try {
    const res = await axios.post(
      "https://api.x.ai/v1/chat/completions",
      {
        model: "xai-claude-3-haiku", 
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 512
      },
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.XAI_API_KEY}`
        }
      }
    );
    return res.data.choices?.[0]?.message?.content?.trim() || null;
  } catch (err) {
    console.warn("XAI text failed:", err.message);
    return null;
  }
}

// ========== Groq Completion ==========
async function groqComplete(prompt) {
  try {
    const res = await axios.post(
      "https://api.groq.com/openai/v1/chat/completions",
      {
        model: "llama3-70b-8192",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 512
      },
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.GROQ_API_KEY}`
        }
      }
    );
    return res.data.choices?.[0]?.message?.content?.trim() || null;
  } catch (err) {
    console.warn("Groq text failed:", err.message);
    return null;
  }
}

// ========== Together AI Completion ==========
const TOGETHER_MODELS = [
  "meta-llama/Llama-3.3-70B-Instruct-Turbo-Free",
  "meta-llama/Llama-Vision-Free",
  "lgai/exaone-3-5-32b-instruct",
  "lgai/exaone-deep-32b",
  "arcee-ai/AFM-4.5B-Preview",
  "deepseek-ai/DeepSeek-R1-Distill-Llama-70B-free",
];

async function togetherComplete(prompt, model) {
  try {
    const res = await axios.post(
      "https://api.together.xyz/v1/chat/completions",
      {
        model,
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 512
      },
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.TOGETHER_API_KEY}`
        }
      }
    );
    return res.data.choices?.[0]?.message?.content?.trim() || null;
  } catch (err) {
    console.warn(`Together model ${model} failed:`, err.message);
    return null;
  }
}

// ========== OpenRouter Completion ==========
const OPENROUTER_MODELS = [
  "openai/gpt-4.1-nano",
  "openai/gpt-4o-mini",
  "openai/gpt-4o",
  "anthropic/claude-3.5-sonnet",
  "anthropic/claude-3-haiku",
  "meta-llama/llama-3.1-8b-instruct",
  "meta-llama/llama-3.1-70b-instruct",
  "google/gemini-pro",
  "mistralai/mistral-7b-instruct",
  "microsoft/wizardlm-2-8x22b",
  "google/gemini-2.0-flash-exp:free",
  "mistralai/mistral-small-24b-instruct-2501:free",
  "qwen/qwen3-30b-a3b:free",
  "sarvamai/sarvam-m:free",
  "qwen/qwen3-30b-a3b:free"
];

async function openrouterComplete(prompt, model) {
  const keys = [process.env.OPENROUTER_API_KEY, process.env.OPENROUTER_API_KEY_SECONDARY];
  
  for (const key of keys) {
    if (!key) continue;
    
    try {
      const res = await axios.post(
        "https://openrouter.ai/api/v1/chat/completions",
        {
          model: model || OPENROUTER_MODELS[0],
          messages: [{ role: "user", content: prompt }],
          temperature: 0.7,
          max_tokens: 512
        },
        {
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${key}`
          }
        }
      );
      const text = res.data.choices?.[0]?.message?.content?.trim();
      if (text) return text;
    } catch (err) {
      console.warn(`OpenRouter model ${model} failed:`, err.message);
    }
  }
  return null;
}

// ========== DeepSeek Completion ==========
async function deepseekComplete(prompt) {
  try {
    const res = await axios.post(
      "https://api.deepseek.com/v1/chat/completions",
      {
        model: "deepseek-chat",
        messages: [{ role: "user", content: prompt }],
        temperature: 0.7,
        max_tokens: 512
      },
      {
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${process.env.DEEPSEEK_API_KEY}`
        }
      }
    );
    return res.data.choices?.[0]?.message?.content?.trim() || null;
  } catch (err) {
    console.warn("DeepSeek text failed:", err.message);
    return null;
  }
}

// ========== Local Agent Completion ==========
export async function localAgentComplete(prompt) {
  try {
    const res = await axios.post(
      "http://103.86.176.140:37898/chat",
      { message: prompt, user_id: 1 },
      { headers: { "Content-Type": "application/json" }}
    );
    return res.data?.response?.trim() || res.data?.response?.trim() || null;
  } catch (err) {
    console.warn("Local Agent failed:", err.message);
    return null;
  }
}

// ========== News Query Handler ==========
export async function handleNewsQuery(userPrompt, options = {}) {
  // Step 1: Get the real news summary from the agent
  const agentReply = await localAgentComplete(userPrompt);
  if (!agentReply) {
    return {
      text: "Sorry, I couldn't fetch the latest news right now.",
      model: "local-agent"
    };
  }
  // Step 2: Pass the agent's reply to the AI model for a friendly response
  const { text, model } = await aiCompleteHuman(agentReply, options);
  return { text, model };
}

// ========== Unified AI Completion ==========
export async function aiComplete(prompt, options = {}) {
  const model = options.model || MODEL;

  const tryList = [
    () => model === "openrouter" && openrouterComplete(prompt),
    () => model === "gemini" && geminiComplete(prompt),
    () => model === "deepseek" && deepseekComplete(prompt),
    () => model === "together" && togetherComplete(prompt, TOGETHER_MODELS[0]),
    () => model === "xai" && xaiComplete(prompt),
    () => model === "groq" && groqComplete(prompt)
  ];

  // Add fallback chain
  tryList.push(() => xaiComplete(prompt));
  tryList.push(() => groqComplete(prompt));
  for (const om of OPENROUTER_MODELS) {
    tryList.push(() => openrouterComplete(prompt, om));
  }
  for (const tm of TOGETHER_MODELS) {
    tryList.push(() => togetherComplete(prompt, tm));
  }
  tryList.push(() => deepseekComplete(prompt));
  tryList.push(() => geminiComplete(prompt)); // fallback again to Gemini

  for (const fn of tryList) {
    try {
      const result = await fn();
      if (typeof result === 'string' && result.trim()) return { text: result, model };
    } catch (_) { }
  }

  return { text: "Kshama kijiye, abhi main jawab nahi de sakta.", model };
}

export async function textCompleteWithFallback(prompt) {
  const { text } = await aiComplete(prompt);
  return text;
}

// ========== Main Human-like Response ==========
export async function aiCompleteHuman(userPrompt, options = {}) {
  const prompt = options.systemPrompt || `
${userPrompt}
You are Nityasha, a friendly and helpful assistant. Talk like a real person having a casual conversation with a friend. Be natural, warm, and conversational in Hindi.

CONVERSATION STYLE:
- Talk like you're chatting with a friend on WhatsApp or phone
- Use natural Hindi expressions and casual language
- Be warm, friendly, and helpful
- Don't sound robotic or formal
- Use contractions and natural speech patterns
- Be conversational, not like a textbook

IMPORTANT RULES:
- Do NOT use emojis or markdown
- Keep replies natural and conversational
- Sound like a real person, not an AI assistant
Now respond naturally like you're talking to a friend:
`;
  const { text, model } = await aiComplete(prompt, options);
  return { text, model };
}

export function getCurrentAIModel() {
  return MODEL;
}

// Utility function to filter search results using AI
export async function filterResultsWithAI(results, userQuestion) {
  if (!results || results.length === 0) return [];
  const prompt = `Analyze these search results for the question: "${userQuestion}"

Search Results:
${results.map((r, i) => `${i + 1}. ${r.title}\n${r.snippet}\n${r.link}`).join('\n\n')}

Return ONLY the numbers of results that DIRECTLY answer the user's question. 
If the results are irrelevant, outdated, or don't answer the question, return "NONE".
Be very strict - only return results that are clearly relevant.

Format: Just numbers separated by commas, like: 1,3,5
If none are relevant: NONE

Relevant result numbers:`;
  try {
    const responseObj = await aiComplete(prompt);
    const response = typeof responseObj === 'string' ? responseObj : responseObj.text;
    if (!response || typeof response !== 'string') return [];
    const relevantNumbers = response.match(/\d+/g) || [];
    if (relevantNumbers.length === 0 || response.toLowerCase().includes('none')) {
      return [];
    } 
    return relevantNumbers
      .map(num => parseInt(num) - 1) // Convert to 0-based index
      .filter(index => index >= 0 && index < results.length)
      .map(index => results[index]);
  } catch (error) {
    console.error('AI filtering failed:', error);
    return []; // Return empty if AI filtering fails
  }
}

// ========== AI-based Agent Routing Helper (with examples and logging) ==========
async function shouldUseAgentAI(userPrompt) {
  const routingPrompt = `
You are an AI router. Your job is to decide if a user's message should be handled by a special agent/tool or by the normal AI chat.

Rules:
- If the user's message is about news, latest information, weather, math, calculations, history, or any factual lookup, reply YES.
- If the user is just having a normal conversation, greeting, or casual/personal talk (like 'hi', 'hello', 'how are you', 'good morning', 'what's up', 'tell me a story', etc.), reply NO.
- If you are unsure, reply NO.
- Reply with only YES or NO. Do not explain your answer.

User message: ${userPrompt}
Your answer:
`;
  const { text } = await aiComplete(routingPrompt);
  const decision = (text || '').trim().toUpperCase().startsWith('Y');
  console.log('[ROUTER] AI routing decision:', decision ? 'AGENT' : 'AI', '| UserPrompt:', userPrompt, '| AI said:', text);
  return decision;
}

// ========== Smart Unified Completion (AI-based) ==========
export async function smartComplete(userPrompt, options = {}) {
  // Use AI to decide if agent should be used
  const useAgent = await shouldUseAgentAI(userPrompt);
  console.log('[SMART] Should use agent:', useAgent, '| UserPrompt:', userPrompt);

  if (useAgent) {
    // Use the agent+AI flow
    const agentReply = await localAgentComplete(userPrompt);
    console.log('[SMART] Agent reply:', agentReply);
    if (!agentReply) {
      console.log('[SMART] Agent failed to provide a reply.');
      return {
        text: "Sorry, I couldn't fetch the latest information right now.",
        model: "local-agent"
      };
    }
    // Use the systemPrompt for the final AI response if provided
    const { text, model } = await aiCompleteHuman(agentReply, options);
    console.log('[SMART] Final AI response (after agent):', text);
    return { text, model };
  } else {
    // Use the systemPrompt for the normal AI flow if provided
    const { text, model } = await aiCompleteHuman(userPrompt, options);
    console.log('[SMART] Final AI response (no agent):', text);
    return { text, model };
  }
}
