{"version": 3, "file": "NLCasualDateTimeParser.js", "sourceRoot": "", "sources": ["../../../../../src/locales/nl/parsers/NLCasualDateTimeParser.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,sCAAsC,EAAE,MAAM,wDAAwD,CAAC;AAChH,OAAO,EAAE,QAAQ,EAAE,MAAM,gBAAgB,CAAC;AAC1C,OAAO,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,MAAM,sBAAsB,CAAC;AAC3E,OAAO,KAAK,MAAM,OAAO,CAAC;AAqB1B,MAAM,UAAU,GAAG,CAAC,CAAC;AACrB,MAAM,iBAAiB,GAAG,CAAC,CAAC;AAE5B,MAAM,CAAC,OAAO,OAAO,sBAAuB,SAAQ,sCAAsC;IACtF,YAAY,CAAC,OAAuB;QAChC,OAAO,qEAAqE,CAAC;IACjF,CAAC;IAED,YAAY,CAAC,OAAuB,EAAE,KAAuB;QACzD,MAAM,QAAQ,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,QAAQ,GAAG,KAAK,CAAC,iBAAiB,CAAC,CAAC,WAAW,EAAE,CAAC;QACxD,MAAM,SAAS,GAAG,OAAO,CAAC,uBAAuB,EAAE,CAAC;QACpD,MAAM,UAAU,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE1C,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,UAAU;gBACX,iBAAiB,CAAC,SAAS,EAAE,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBACxD,MAAM;YACV,KAAK,KAAK;gBACN,iBAAiB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACzC,MAAM;YACV,KAAK,QAAQ;gBACT,gBAAgB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;gBACxC,MAAM;QACd,CAAC;QAED,QAAQ,QAAQ,EAAE,CAAC;YACf,KAAK,SAAS;gBACV,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;gBAC3B,MAAM;YACV,KAAK,QAAQ;gBACT,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,MAAM;YACV,KAAK,UAAU;gBACX,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,MAAM;YAEV,KAAK,OAAO;gBACR,SAAS,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,EAAE,CAAC,CAAC;gBACzC,SAAS,CAAC,KAAK,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;gBAC5B,MAAM;QACd,CAAC;QAED,OAAO,SAAS,CAAC;IACrB,CAAC;CACJ"}