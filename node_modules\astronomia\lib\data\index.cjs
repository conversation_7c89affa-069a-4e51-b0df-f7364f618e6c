'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var deltat = require('./deltat.cjs');
var vsop87Bearth = require('./vsop87Bearth.cjs');
var vsop87Bjupiter = require('./vsop87Bjupiter.cjs');
var vsop87Bmars = require('./vsop87Bmars.cjs');
var vsop87Bmercury = require('./vsop87Bmercury.cjs');
var vsop87Bneptune = require('./vsop87Bneptune.cjs');
var vsop87Bsaturn = require('./vsop87Bsaturn.cjs');
var vsop87Buranus = require('./vsop87Buranus.cjs');
var vsop87Bvenus = require('./vsop87Bvenus.cjs');
var vsop87Dearth = require('./vsop87Dearth.cjs');
var vsop87Djupiter = require('./vsop87Djupiter.cjs');
var vsop87Dmars = require('./vsop87Dmars.cjs');
var vsop87Dmercury = require('./vsop87Dmercury.cjs');
var vsop87Dneptune = require('./vsop87Dneptune.cjs');
var vsop87Dsaturn = require('./vsop87Dsaturn.cjs');
var vsop87Duranus = require('./vsop87Duranus.cjs');
var vsop87Dvenus = require('./vsop87Dvenus.cjs');
var elpMppDe = require('./elpMppDe.cjs');
var elpMppDeFull = require('./elpMppDeFull.cjs');

var index = {
  deltat: deltat["default"],
  earth: vsop87Bearth["default"],
  jupiter: vsop87Bjupiter["default"],
  mars: vsop87Bmars["default"],
  mercury: vsop87Bmercury["default"],
  neptune: vsop87Bneptune["default"],
  saturn: vsop87Bsaturn["default"],
  uranus: vsop87Buranus["default"],
  venus: vsop87Bvenus["default"],
  vsop87Bearth: vsop87Bearth["default"],
  vsop87Bjupiter: vsop87Bjupiter["default"],
  vsop87Bmars: vsop87Bmars["default"],
  vsop87Bmercury: vsop87Bmercury["default"],
  vsop87Bneptune: vsop87Bneptune["default"],
  vsop87Bsaturn: vsop87Bsaturn["default"],
  vsop87Buranus: vsop87Buranus["default"],
  vsop87Bvenus: vsop87Bvenus["default"],
  vsop87Dearth: vsop87Dearth["default"],
  vsop87Djupiter: vsop87Djupiter["default"],
  vsop87Dmars: vsop87Dmars["default"],
  vsop87Dmercury: vsop87Dmercury["default"],
  vsop87Dneptune: vsop87Dneptune["default"],
  vsop87Dsaturn: vsop87Dsaturn["default"],
  vsop87Duranus: vsop87Duranus["default"],
  vsop87Dvenus: vsop87Dvenus["default"],
  elpMppDe: elpMppDe["default"],
  elpMppDeFull: elpMppDeFull["default"],
};

exports["default"] = index;
