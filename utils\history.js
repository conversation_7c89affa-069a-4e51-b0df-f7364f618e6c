import { redisClient } from "../config/redis.js";

const MAX_HISTORY = 10;

export async function getChatHistory(userId) {
  const history = await redisClient.lRange(`chat:${userId}`, 0, MAX_HISTORY - 1);
  // Parse each entry as JSON, fallback to old format if needed
  return history.reverse().map(entry => {
    try {
      return JSON.parse(entry);
    } catch {
      // fallback for old entries
      const userMatch = entry.match(/^User: (.*)$/m);
      const assistantMatch = entry.match(/^Assistant: (.*)$/m);
      return {
        user: userMatch ? userMatch[1] : '',
        assistant: assistantMatch ? assistantMatch[1] : '',
        modelUsed: undefined,
        modelName: undefined
      };
    }
  });
}

export async function addToChatHistory(userId, userMessage, aiResponse, modelUsed, modelName, toolsUsed = []) {
  const entry = JSON.stringify({
    user: userMessage,
    assistant: aiResponse,
    modelUsed,
    modelName,
    toolsUsed
  });
  await redisClient.lPush(`chat:${userId}`, entry);
  await redisClient.lTrim(`chat:${userId}`, 0, MAX_HISTORY - 1);
}
