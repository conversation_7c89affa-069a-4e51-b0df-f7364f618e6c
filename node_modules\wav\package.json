{"name": "wav", "description": "`Reader` and `Writer` streams for Microsoft WAVE audio files", "version": "1.0.2", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "repository": "TooTallNate/node-wav", "dependencies": {"buffer-alloc": "^1.1.0", "buffer-from": "^1.0.0", "debug": "^2.2.0", "readable-stream": "^1.1.14", "stream-parser": "^0.3.1"}, "devDependencies": {"mocha": "^2.5.3", "semistandard": "^12.0.1"}, "scripts": {"test": "semistandard && mocha"}}