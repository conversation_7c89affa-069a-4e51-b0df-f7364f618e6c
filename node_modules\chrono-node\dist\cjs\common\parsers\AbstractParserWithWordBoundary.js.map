{"version": 3, "file": "AbstractParserWithWordBoundary.js", "sourceRoot": "", "sources": ["../../../../src/common/parsers/AbstractParserWithWordBoundary.ts"], "names": [], "mappings": ";;;AAOA,MAAsB,sCAAsC;IAA5D;QAgBY,uBAAkB,GAAY,IAAI,CAAC;QACnC,kBAAa,GAAY,IAAI,CAAC;IA0B1C,CAAC;IAnCG,qBAAqB,CAAC,OAAuB,EAAE,mBAA2B;QACtE,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,KAAK,mBAAmB,CAAC;IAC9D,CAAC;IAED,mBAAmB;QACf,OAAO,SAAS,CAAC;IACrB,CAAC;IAKD,OAAO,CAAC,OAAuB;QAC3B,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAChE,OAAO,IAAI,CAAC,aAAa,CAAC;YAC9B,CAAC;QACL,CAAC;QACD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACrD,IAAI,CAAC,aAAa,GAAG,IAAI,MAAM,CAC3B,GAAG,IAAI,CAAC,mBAAmB,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,EAChE,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAChC,CAAC;QACF,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,OAAO,CAAC,OAAuB,EAAE,KAAuB;;QACpD,MAAM,MAAM,GAAG,MAAA,KAAK,CAAC,CAAC,CAAC,mCAAI,EAAE,CAAC;QAC9B,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC;QAC1C,KAAK,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACpC,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC;QAED,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;IAC7C,CAAC;CACJ;AA3CD,wFA2CC"}