import { DynamicTool } from "langchain/tools";
import { getMysqlConnection } from "./db.js";
import { saveMemory } from "./memory.js";

export const weatherTool = new DynamicTool({
  name: "weather",
  description: "Get current weather info for a city. Input: { city: string }",
  func: async ({ city }) => {
    if (!city) return "Please provide a city name.";
    const apiKey = process.env.OPENWEATHER_API_KEY;
    if (!apiKey) return "Weather API key not set.";
    try {
      const response = await fetch(
        `https://api.openweathermap.org/data/2.5/weather?q=${encodeURIComponent(city)}&appid=${apiKey}&units=metric&lang=hi`
      );
      if (!response.ok) return `Couldn't fetch weather for ${city}.`;
      const data = await response.json();
      const temp = data.main?.temp;
      const desc = data.weather?.[0]?.description;
      return `${city} ka mausam: ${desc}, ${temp}°C.`;
    } catch (e) {
      return "Weather fetch error.";
    }
  }
});

export const calculatorTool = new DynamicTool({
  name: "calculator",
  description: "Evaluate a simple math expression. Input: { expression: string }",
  func: async ({ expression }) => {
    try {
      if (!/^[\d\s\+\-\*\/\(\)]+$/.test(expression)) return "Invalid expression";
      return eval(expression).toString();
    } catch {
      return "Calculation error";
    }
  }
});

export const wikipediaTool = new DynamicTool({
  name: "wikipedia",
  description: "Get a short summary for a historical or factual topic from Wikipedia. Input: { query: string }",
  func: async ({ query }) => {
    if (!query) return "Please provide a topic to search on Wikipedia.";
    try {
      const response = await fetch(`https://en.wikipedia.org/api/rest_v1/page/summary/${encodeURIComponent(query)}`);
      if (!response.ok) return `No Wikipedia summary found for '${query}'.`;
      const data = await response.json();
      return data.extract || `No summary found for '${query}'.`;
    } catch {
      return "Wikipedia fetch error.";
    }
  }
});

export const duckDuckGoTool = new DynamicTool({
  name: "duckduckgo",
  description: "Search the web for up-to-date information using DuckDuckGo. Input: { query: string }",
  func: async ({ query }) => {
    if (!query) return "Please provide a search query.";
    try {
      const response = await fetch(`https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&no_redirect=1&no_html=1`);
      if (!response.ok) return `No DuckDuckGo results for '${query}'.`;
      const data = await response.json();
      if (data.AbstractText) return data.AbstractText;
      if (data.RelatedTopics && data.RelatedTopics.length > 0 && data.RelatedTopics[0].Text) return data.RelatedTopics[0].Text;
      return `No relevant DuckDuckGo results for '${query}'.`;
    } catch {
      return "DuckDuckGo fetch error.";
    }
  }
});

export const mysqlSearchTool = new DynamicTool({
  name: "mysqlsearch",
  description: "Search for hotels, courier, etc. in the MySQL 'info' table using a full-text search. Input: { query: string }",
  func: async ({ query }) => {
    if (!query) return "Please provide a search query.";
    try {
      const conn = await getMysqlConnection();
      const [rows] = await conn.execute(
        `SELECT *, MATCH(name, about, tags, city, address) AGAINST (? IN NATURAL LANGUAGE MODE) AS score
         FROM info
         WHERE MATCH(name, about, tags, city, address) AGAINST (? IN NATURAL LANGUAGE MODE)
         ORDER BY score DESC
         LIMIT 5`,
        [query, query]
      );
      await conn.end();
      if (!rows.length) return `No results found for '${query}'.`;
      return rows.map(r => `${r.name} (${r.city}): ${r.about}`).join("\n");
    } catch (e) {
      return "MySQL search error.";
    }
  }
});

export const superDataTool = new DynamicTool({
  name: "superDataTool",
  description: "Returns a large JSON object with news, summaries, and sources. Input: { question: string } (or { city: string } for backward compatibility)",
  func: async ({ question, city }) => {
    const apiUrl = "https://core.ask.nityasha.com/ask";
    // Prefer question if present, else fallback to city
    try {
      const response = await fetch(apiUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "authorization": "Bearer 948e46a350813838f664dcc5e167f523"
        },
        body: JSON.stringify({ question })
      });
      if (!response.ok) return `API error: ${response.status}`;
      const data = await response.json();
      // Return the full response or extract relevant fields if needed
      return data;
    } catch (e) {
      return "superDataTool API fetch error.";
    }
  }
});

export const saveMemoryTool = {
  name: "saveMemoryTool",
  description: "Save a memory for the user. Input: { user_id: string, key: string, value: string }",
  func: async ({ user_id, key, value }) => {
    if (!user_id || !key || !value) return "user_id, key, and value are required.";
    try {
      const result = await saveMemory(user_id, key, value);
      return result ? "Memory saved!" : "Failed to save memory.";
    } catch (e) {
      return "Failed to save memory.";
    }
  }
};

export const tools = [
  weatherTool,
  calculatorTool,
  wikipediaTool,
  duckDuckGoTool,
  mysqlSearchTool,
  superDataTool,
  saveMemoryTool,
  {
    name: "getCurrentTimeIndia",
    description: "Get the current time in India (IST)",
    func: async () => {
      const now = new Date();
      // Convert to IST (UTC+5:30)
      const utc = now.getTime() + (now.getTimezoneOffset() * 60000);
      const istOffset = 5.5 * 60 * 60 * 1000;
      const istTime = new Date(utc + istOffset);
      return istTime.toLocaleTimeString("en-IN", { hour: '2-digit', minute: '2-digit', hour12: true, timeZone: 'Asia/Kolkata' });
    }
  }
]; 