import axios from "axios";

export function checkTithiRequest(message) {
  const keywords = ["tithi", "panchang", "amava<PERSON>a", "shukla", "krishna", "tithi kya hai"];
  return keywords.some(k => message.toLowerCase().includes(k));
}

export async function getTodayTithiIST(date = null, lat = 28.6139, lon = 77.2090) {
  try {
    const istDate = date || new Date(new Date().toLocaleString("en-US", { timeZone: "Asia/Kolkata" })).toISOString().split("T")[0];
    const url = `http://103.86.176.140:1237/tithi?date=${istDate}&lat=${lat}&lon=${lon}`;
    const res = await axios.get(url, { timeout: 8000 });
    const { tithi, paksha } = res.data;
    return {
      date: istDate,
      tithi,
      paksha,
      message: `${istDate} ko ${paksha} paksha ki ${tithi} tithi hai.`,
      success: true
    };
  } catch (err) {
    return {
      date: date || new Date().toISOString().split("T")[0],
      tithi: "unknown",
      paksha: "unknown",
      message: "Tithi ki jankari uplabdh nahi hai.",
      success: false
    };
  }
}
