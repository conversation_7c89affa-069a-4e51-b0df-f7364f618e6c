{"version": 3, "file": "SpeakLiveClient.js", "sourceRoot": "", "sources": ["../../../src/packages/SpeakLiveClient.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAC;AAC1D,OAAO,EAAE,aAAa,EAAE,MAAM,cAAc,CAAC;AAG7C;;;;;;;;;;GAUG;AACH,MAAM,OAAO,eAAgB,SAAQ,kBAAkB;IAGrD;;;;;;OAMG;IACH,YACE,OAA8B,EAC9B,eAA+C,EAAE,EACjD,WAAmB,gBAAgB;QAEnC,KAAK,CAAC,OAAO,CAAC,CAAC;QAdV,cAAS,GAAW,OAAO,CAAC;QAgBjC,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;IACvC,CAAC;IAED;;;;;;;OAOG;IACI,eAAe;QACpB,iFAAiF;QACjF,IAAI,CAAC,qBAAqB,CAAC;YACzB,IAAI,EAAE,aAAa,CAAC,IAAI;YACxB,KAAK,EAAE,aAAa,CAAC,KAAK;YAC1B,KAAK,EAAE,aAAa,CAAC,KAAK;SAC3B,CAAC,CAAC;QAEH,qDAAqD;QACrD,IAAI,IAAI,CAAC,IAAI,EAAE;YACb,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAmB,EAAE,EAAE;gBAC5C,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;YAC5B,CAAC,CAAC;SACH;IACH,CAAC;IAED;;;OAGG;IACO,iBAAiB,CAAC,IAAS;QACnC,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,QAAQ,EAAE;YACxC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;SACzC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACxC;aAAM,IAAI,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC,OAAO,EAAE;YAC9C,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;SACxC;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;SAC1C;IACH,CAAC;IAED;;;OAGG;IACO,mBAAmB,CAAC,IAAY;QACxC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IACvC,CAAC;IAED;;;;OAIG;IACI,QAAQ,CAAC,IAAY;QAC1B,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;YACb,IAAI;SACL,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,KAAK;QACV,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,YAAY;QACjB,IAAI,CAAC,IAAI,CACP,IAAI,CAAC,SAAS,CAAC;YACb,IAAI,EAAE,OAAO;SACd,CAAC,CACH,CAAC;IACJ,CAAC;IAED;;;OAGG;IACO,aAAa,CAAC,KAAmB;;QACzC,IAAI,OAAO,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE;YAClC,IAAI;gBACF,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACpC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;aAC9B;YAAC,OAAO,KAAK,EAAE;gBACd,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;oBAC7B,KAAK;oBACL,OAAO,EAAE,iCAAiC;oBAC1C,KAAK;oBACL,GAAG,EAAE,MAAA,IAAI,CAAC,IAAI,0CAAE,GAAG;oBACnB,UAAU,EAAE,MAAA,IAAI,CAAC,IAAI,0CAAE,UAAU;oBACjC,IAAI,EACF,CAAA,MAAA,KAAK,CAAC,IAAI,0CAAE,QAAQ,GAAG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;wBACxC,CAAC,CAAA,MAAA,KAAK,CAAC,IAAI,0CAAE,QAAQ,GAAG,MAAM,IAAG,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC;iBACrD,CAAC,CAAC;aACJ;SACF;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,EAAE;YACrC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gBACvC,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC;YAChD,CAAC,CAAC,CAAC;SACJ;aAAM,IAAI,KAAK,CAAC,IAAI,YAAY,WAAW,EAAE;YAC5C,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;SACnD;aAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;SACtC;aAAM;YACL,OAAO,CAAC,GAAG,CAAC,4BAA4B,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE;gBAC7B,KAAK;gBACL,OAAO,EAAE,6BAA6B;gBACtC,GAAG,EAAE,MAAA,IAAI,CAAC,IAAI,0CAAE,GAAG;gBACnB,UAAU,EAAE,MAAA,IAAI,CAAC,IAAI,0CAAE,UAAU;gBACjC,QAAQ,EAAE,OAAO,KAAK,CAAC,IAAI;aAC5B,CAAC,CAAC;SACJ;IACH,CAAC;CACF"}