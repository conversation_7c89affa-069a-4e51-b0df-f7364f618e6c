{"version": 3, "file": "mergingCalculation.js", "sourceRoot": "", "sources": ["../../../src/calculation/mergingCalculation.ts"], "names": [], "mappings": ";;AAIA,kDA0BC;AAED,wDAkDC;AAjFD,oCAAoC;AACpC,0CAAqE;AAErE,SAAgB,mBAAmB,CAAC,UAAyB,EAAE,UAAyB;IACpF,MAAM,MAAM,GAAG,UAAU,CAAC,KAAK,EAAE,CAAC;IAClC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;IACnC,MAAM,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC;IAEnC,MAAM,CAAC,KAAK,GAAG,sBAAsB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC5D,IAAI,UAAU,CAAC,GAAG,IAAI,IAAI,IAAI,UAAU,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QACnD,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;QAC3E,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,CAAC,GAAG,CAAC;QAC3E,MAAM,WAAW,GAAG,sBAAsB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAE7D,IAAI,UAAU,CAAC,GAAG,IAAI,IAAI,IAAI,WAAW,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC;YAGzF,MAAM,SAAS,GAAG,WAAW,CAAC,KAAK,EAAE,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;YACpD,IAAI,WAAW,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC/B,IAAA,yBAAiB,EAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACJ,IAAA,wBAAgB,EAAC,WAAW,EAAE,SAAS,CAAC,CAAC;YAC7C,CAAC;QACL,CAAC;QAED,MAAM,CAAC,GAAG,GAAG,WAAW,CAAC;IAC7B,CAAC;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AAED,SAAgB,sBAAsB,CAClC,aAAgC,EAChC,aAAgC;IAEhC,MAAM,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC;IAEhD,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;QAClC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC5D,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAEhE,IAAI,aAAa,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;YACpC,iBAAiB,CAAC,MAAM,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;YAEhE,IAAI,aAAa,CAAC,SAAS,CAAC,aAAa,CAAC,EAAE,CAAC;gBACzC,iBAAiB,CAAC,MAAM,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;YAC9E,CAAC;iBAAM,CAAC;gBACJ,iBAAiB,CAAC,KAAK,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;YAC7E,CAAC;QACL,CAAC;aAAM,CAAC;YACJ,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;YAC/D,iBAAiB,CAAC,KAAK,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;QAC7E,CAAC;IACL,CAAC;SAAM,CAAC;QACJ,iBAAiB,CAAC,KAAK,CAAC,MAAM,EAAE,aAAa,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC;QAC3D,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC/D,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,aAAa,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC/D,iBAAiB,CAAC,KAAK,CAAC,aAAa,EAAE,aAAa,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC,CAAC;IAC7E,CAAC;IAED,IAAI,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,EAAE,CAAC;QAC5C,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,EAAE,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC,CAAC;IACpF,CAAC;IAED,IAAI,aAAa,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,CAAC;QACtC,iBAAiB,CAAC,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IACxE,CAAC;SAAM,IAAI,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,IAAI,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE,CAAC;QAC5F,iBAAiB,CAAC,KAAK,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC;IACvE,CAAC;IAED,IAAI,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,gBAAQ,CAAC,EAAE,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,EAAE,CAAC;QACzF,IAAI,aAAa,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,CAAC;YAClC,iBAAiB,CAAC,MAAM,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QACzE,CAAC;aAAM,CAAC;YACJ,iBAAiB,CAAC,KAAK,CAAC,MAAM,EAAE,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;QACxE,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IAChD,iBAAiB,CAAC,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,CAAC;IAChD,OAAO,iBAAiB,CAAC;AAC7B,CAAC"}